{"hideRem": "Hide REM", "hideOthers": "Hide Others", "showAll": "Show All", "quitRem": "Quit REM", "aboutRem": "About REM", "edit": "Edit", "cut": "Cut", "copy": "Copy", "paste": "Paste", "delete": "Delete", "selectAll": "Select All", "help": "Help", "toggleDevtools": "Toggle Developer Tools", "reportIssue": "Report Issue", "checkUpdate": "Check for Updates", "donate": "Donate", "tools": "Tools", "terminal": "Terminal", "clear": "Clear", "settings": "Settings", "appearance": "Appearance", "theme": "Theme", "light": "Light", "dark": "Dark", "sysPreference": "System Preference", "language": "Language", "restartRem": "Restart REM", "requireReload": "Requires reload", "config": "Config", "back": "Back", "forward": "Forward", "up": "Up", "refresh": "Refresh", "iconView": "Icon View", "listView": "List View", "localDisk": "Local Disk", "openInCurWin": "Open in Current Window", "openInNewWin": "Open in New Window", "reqErr": "Request Error", "open": "Open", "rcloneCli": "Rclone Command Line Interface", "duplicate": "Duplicate", "deleteConfigConfirm": "Are you sure you want to delete {{name}}?", "filter": "Filter", "newFolder": "New Folder", "newFolderName": "New Folder Name", "deleteFileConfirm": "Are you sure you want to delete the file {{name}}?", "job": "Job", "source": "Source", "destination": "Destination", "jobId": "Job ID", "upload": "Upload", "type": "Type", "status": "Status", "running": "Running", "success": "Success", "fail": "Fail", "duration": "Duration", "startTime": "Start Time", "download": "Download", "rename": "<PERSON><PERSON>", "newFileName": "New File Name", "move": "Move", "file": "File", "size": "Size", "speed": "Speed", "clearFinished": "Clear Finished", "stop": "Stop", "stopAll": "Stop All", "cancel": "Cancel", "version": "Version", "updateNotAvailable": "There are currently no updates available", "updateAvailable": "A new version is available, do you want to download it?", "updateErr": "Failed to check for updates", "ok": "OK", "usedStorage": "{{storage}} used", "totalStorage": "{{storage}} total", "selectForSync": "Select for Synchronize", "sync": "Synchronize", "getPublicLink": "Get Public Link", "publicLink": "Public Link", "copied": "<PERSON>pied", "mount": "Mount", "mountManager": "Mount Manager", "browse": "Browse", "mountPoint": "Mount Point", "mounted": "Mounted", "unmounted": "Unmounted", "openDir": "Open Directory", "deleteAllMounts": "Delete All Mounts", "deleteAllMountsConfirm": "Are you sure you want to delete all mounts?", "mountErr": "Failed to mount {{mountPoint}}", "unmount": "Unmount", "winfspNotFound": "WinFsp not found, do you want to install it?", "macfuseNotFound": "macFUSE not found, do you want to install it?", "rclonePath": "Rclone Path", "autoMount": "Auto mount", "startup": "Startup", "add": "Add", "addConfig": "Add Config", "name": "Name", "provider": "Provider", "nameRequired": "Name is required", "configExist": "Config {{name}} already exists", "newConfigName": "New Config Name", "preview": "Preview", "fileNotSelected": "File not selected", "noPreview": "No preview", "configPath": "Config Path", "show": "Show", "openAtLogin": "Open at login"}