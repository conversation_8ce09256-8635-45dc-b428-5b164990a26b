version: '3.8'

services:
  rclone:
    image: rclone/rclone:latest
    container_name: rclone-web
    restart: unless-stopped
    ports:
      - "5572:5572"
    volumes:
      - ./rclone-config:/config/rclone
      - ./rclone-data:/data
      - /etc/passwd:/etc/passwd:ro
      - /etc/group:/etc/group:ro
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Asia/Shanghai
    command: >
      rcd 
      --rc-web-gui 
      --rc-addr=0.0.0.0:5572 
      --rc-user=admin 
      --rc-pass=your_secure_password_here
      --rc-allow-origin=*
      --config=/config/rclone/rclone.conf
      --cache-dir=/data/cache
      --log-level=INFO
    networks:
      - rclone-net

  # 可选：添加 Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: rclone-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - rclone
    networks:
      - rclone-net

networks:
  rclone-net:
    driver: bridge

volumes:
  rclone-config:
  rclone-data:
