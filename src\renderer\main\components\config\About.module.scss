@use '../../../theme' as *;

.container {
  padding: #{$padding-x-s}px;
}

.storage {
  font-size: #{$font-size-s-m}px;
  margin-bottom: #{$margin-x-x-s}px;
  display: flex;
}

.used,
.total {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  min-width: 0;
}

.used {
  text-align: left;
}

.total {
  text-align: right;
}

.bar {
  height: 8px;
  border-radius: 4px;
  background: var(--color-bg-container-darker);
  overflow: hidden;
}

.bar-used {
  height: 100%;
  background: var(--color-primary);
}
