/* Netflix 风格播放器样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #141414;
    color: #ffffff;
    overflow-x: hidden;
}

/* 导航栏 */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: linear-gradient(180deg, rgba(0,0,0,0.8) 0%, transparent 100%);
    z-index: 1000;
    padding: 15px 0;
    transition: background 0.3s ease;
}

.navbar.scrolled {
    background: rgba(0,0,0,0.95);
    backdrop-filter: blur(10px);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
}

.nav-brand {
    display: flex;
    align-items: center;
    font-size: 24px;
    font-weight: bold;
    color: #e50914;
}

.nav-brand i {
    margin-right: 10px;
    font-size: 28px;
}

.nav-controls {
    display: flex;
    gap: 15px;
}

.nav-btn {
    background: transparent;
    border: none;
    color: #ffffff;
    font-size: 18px;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-btn:hover {
    background: rgba(255,255,255,0.1);
    transform: scale(1.1);
}

/* 主容器 */
.main-container {
    margin-top: 80px;
    min-height: calc(100vh - 80px);
}

/* 文件浏览器 */
.file-browser {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.breadcrumb {
    margin-bottom: 20px;
    padding: 10px 0;
    border-bottom: 1px solid #333;
}

.breadcrumb-item {
    display: inline-block;
    color: #999;
    cursor: pointer;
    margin-right: 10px;
    transition: color 0.3s ease;
}

.breadcrumb-item:hover {
    color: #e50914;
}

.breadcrumb-item.active {
    color: #ffffff;
}

.loading {
    text-align: center;
    padding: 50px;
    font-size: 18px;
}

.loading i {
    font-size: 24px;
    margin-right: 10px;
    color: #e50914;
}

/* 文件网格 */
.file-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    padding: 20px 0;
}

.file-item {
    background: #222;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.file-item:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(229,9,20,0.3);
}

.file-thumbnail {
    width: 100%;
    height: 120px;
    background: #333;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 48px;
    color: #666;
    position: relative;
    overflow: hidden;
}

.file-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.file-thumbnail.video {
    background: linear-gradient(135deg, #e50914, #b20710);
    color: #ffffff;
}

.file-thumbnail.audio {
    background: linear-gradient(135deg, #1db954, #1ed760);
    color: #ffffff;
}

.file-thumbnail.image {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: #ffffff;
}

.file-thumbnail.folder {
    background: linear-gradient(135deg, #4a90e2, #357abd);
    color: #ffffff;
}

.file-info {
    padding: 15px;
}

.file-name {
    font-weight: 500;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-size {
    color: #999;
    font-size: 12px;
}

.play-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(229,9,20,0.9);
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.file-item:hover .play-overlay {
    opacity: 1;
}

.play-overlay i {
    color: #ffffff;
    font-size: 24px;
    margin-left: 3px;
}

/* 视频播放器 */
.video-player-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #000;
    z-index: 2000;
    display: flex;
    flex-direction: column;
}

.video-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
}

#videoPlayer {
    flex: 1;
    width: 100%;
    background: #000;
}

.video-info {
    background: linear-gradient(transparent, rgba(0,0,0,0.9));
    padding: 20px;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    opacity: 1;
    transition: opacity 0.3s ease;
    z-index: 10;
}

.video-player-container:hover .video-info {
    opacity: 1;
}

#videoTitle {
    margin-bottom: 15px;
    font-size: 24px;
}

.video-controls {
    display: flex;
    gap: 15px;
    margin-top: 15px;
    flex-wrap: wrap;
}

/* 添加播放器快捷键提示 */
.video-shortcuts {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 10px;
    border-radius: 5px;
    font-size: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 11;
}

.video-player-container:hover .video-shortcuts {
    opacity: 1;
}

.video-shortcuts div {
    margin-bottom: 5px;
}

/* 音频播放器 */
.audio-player-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #1db954, #1ed760);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.audio-wrapper {
    text-align: center;
    max-width: 500px;
    padding: 40px;
}

.audio-info {
    margin-bottom: 30px;
}

#audioTitle {
    font-size: 28px;
    margin-bottom: 20px;
}

.audio-cover {
    width: 200px;
    height: 200px;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 30px;
    font-size: 80px;
}

#audioPlayer {
    width: 100%;
    margin-bottom: 30px;
}

.audio-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
}

/* 图片查看器 */
.image-viewer {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.95);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-container {
    position: relative;
    max-width: 90%;
    max-height: 90%;
}

#imageDisplay {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.image-controls {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    gap: 10px;
}

/* 控制按钮 */
.control-btn {
    background: rgba(0,0,0,0.7);
    border: none;
    color: #ffffff;
    padding: 12px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.control-btn:hover {
    background: #e50914;
    transform: translateY(-2px);
}

/* 错误提示 */
.error-message {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.8);
    z-index: 3000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.error-content {
    background: #333;
    padding: 30px;
    border-radius: 8px;
    text-align: center;
    max-width: 400px;
}

.error-content i {
    font-size: 48px;
    color: #e50914;
    margin-bottom: 15px;
}

.error-content button {
    background: #e50914;
    border: none;
    color: #ffffff;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 15px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 15px;
    }
    
    .nav-brand {
        font-size: 20px;
    }
    
    .file-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
        padding: 15px 0;
    }
    
    .file-browser {
        padding: 15px;
    }
    
    .video-info {
        padding: 15px;
    }
    
    #videoTitle {
        font-size: 20px;
    }
    
    .audio-wrapper {
        padding: 20px;
    }
    
    #audioTitle {
        font-size: 24px;
    }
    
    .audio-cover {
        width: 150px;
        height: 150px;
        font-size: 60px;
    }
}

@media (max-width: 480px) {
    .file-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 10px;
    }
    
    .file-thumbnail {
        height: 80px;
        font-size: 32px;
    }
    
    .play-overlay {
        width: 40px;
        height: 40px;
    }
    
    .play-overlay i {
        font-size: 16px;
    }
}
