.luna-data-grid {
  td {
    user-select: none;
  }
}

@each $class in (':root', '.-theme-with-dark-background') {
  #{$class} {
    .luna-data-grid-data-container {
      .luna-data-grid-node.luna-data-grid-selectable {
        &:hover {
          background: transparent;
        }
      }
      tr:nth-child(even) {
        background-color: if($class == ':root', #f1f1f1, #2c363e) !important;
      }
    }
    .luna-data-grid {
      .luna-data-grid-node.luna-data-grid-selectable.luna-data-grid-selected {
        color: #fff;
        background-color: var(--color-primary) !important;
      }
    }
  }
}
