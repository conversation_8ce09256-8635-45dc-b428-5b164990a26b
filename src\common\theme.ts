// light
export const colorPrimary = `#60beef`
export const colorSuccess = `#52c41a`
export const colorWarning = `#faad14`
export const colorError = `#ff4d4f`
export const colorInfo = `#1677ff`
export const colorLink = `#1677ff`
export const colorTextBase = `#000`
export const colorBgBase = `#fff`
export const fontFamily = `-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
'Noto Color Emoji'`
export const fontFamilyCode = `'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace`
export const fontSize = `14`
export const lineWidth = `1`
export const lineType = `solid`
export const motionUnit = `0.1`
export const motionBase = `0`
export const motionEaseOutCirc = `cubic-bezier(0.08, 0.82, 0.17, 1)`
export const motionEaseInOutCirc = `cubic-bezier(0.78, 0.14, 0.15, 0.86)`
export const motionEaseOut = `cubic-bezier(0.215, 0.61, 0.355, 1)`
export const motionEaseInOut = `cubic-bezier(0.645, 0.045, 0.355, 1)`
export const motionEaseOutBack = `cubic-bezier(0.12, 0.4, 0.29, 1.46)`
export const motionEaseInBack = `cubic-bezier(0.71, -0.46, 0.88, 0.6)`
export const motionEaseInQuint = `cubic-bezier(0.755, 0.05, 0.855, 0.06)`
export const motionEaseOutQuint = `cubic-bezier(0.23, 1, 0.32, 1)`
export const borderRadius = `6`
export const sizeUnit = `4`
export const sizeStep = `4`
export const sizePopupArrow = `16`
export const controlHeight = `32`
export const zIndexBase = `0`
export const zIndexPopupBase = `1000`
export const opacityImage = `1`
export const wireframe = `false`
export const motion = `true`
export const colorBgContainer = `#fafafa`
export const colorBorder = `#dadada`
export const blue1 = `#e6f4ff`
export const blue2 = `#bae0ff`
export const blue3 = `#91caff`
export const blue4 = `#69b1ff`
export const blue5 = `#4096ff`
export const blue6 = `#1677ff`
export const blue7 = `#0958d9`
export const blue8 = `#003eb3`
export const blue9 = `#002c8c`
export const blue10 = `#001d66`
export const purple1 = `#f9f0ff`
export const purple2 = `#efdbff`
export const purple3 = `#d3adf7`
export const purple4 = `#b37feb`
export const purple5 = `#9254de`
export const purple6 = `#722ed1`
export const purple7 = `#531dab`
export const purple8 = `#391085`
export const purple9 = `#22075e`
export const purple10 = `#120338`
export const cyan1 = `#e6fffb`
export const cyan2 = `#b5f5ec`
export const cyan3 = `#87e8de`
export const cyan4 = `#5cdbd3`
export const cyan5 = `#36cfc9`
export const cyan6 = `#13c2c2`
export const cyan7 = `#08979c`
export const cyan8 = `#006d75`
export const cyan9 = `#00474f`
export const cyan10 = `#002329`
export const green1 = `#f6ffed`
export const green2 = `#d9f7be`
export const green3 = `#b7eb8f`
export const green4 = `#95de64`
export const green5 = `#73d13d`
export const green6 = `#52c41a`
export const green7 = `#389e0d`
export const green8 = `#237804`
export const green9 = `#135200`
export const green10 = `#092b00`
export const magenta1 = `#fff0f6`
export const magenta2 = `#ffd6e7`
export const magenta3 = `#ffadd2`
export const magenta4 = `#ff85c0`
export const magenta5 = `#f759ab`
export const magenta6 = `#eb2f96`
export const magenta7 = `#c41d7f`
export const magenta8 = `#9e1068`
export const magenta9 = `#780650`
export const magenta10 = `#520339`
export const pink1 = `#fff0f6`
export const pink2 = `#ffd6e7`
export const pink3 = `#ffadd2`
export const pink4 = `#ff85c0`
export const pink5 = `#f759ab`
export const pink6 = `#eb2f96`
export const pink7 = `#c41d7f`
export const pink8 = `#9e1068`
export const pink9 = `#780650`
export const pink10 = `#520339`
export const red1 = `#fff1f0`
export const red2 = `#ffccc7`
export const red3 = `#ffa39e`
export const red4 = `#ff7875`
export const red5 = `#ff4d4f`
export const red6 = `#f5222d`
export const red7 = `#cf1322`
export const red8 = `#a8071a`
export const red9 = `#820014`
export const red10 = `#5c0011`
export const orange1 = `#fff7e6`
export const orange2 = `#ffe7ba`
export const orange3 = `#ffd591`
export const orange4 = `#ffc069`
export const orange5 = `#ffa940`
export const orange6 = `#fa8c16`
export const orange7 = `#d46b08`
export const orange8 = `#ad4e00`
export const orange9 = `#873800`
export const orange10 = `#612500`
export const yellow1 = `#feffe6`
export const yellow2 = `#ffffb8`
export const yellow3 = `#fffb8f`
export const yellow4 = `#fff566`
export const yellow5 = `#ffec3d`
export const yellow6 = `#fadb14`
export const yellow7 = `#d4b106`
export const yellow8 = `#ad8b00`
export const yellow9 = `#876800`
export const yellow10 = `#614700`
export const volcano1 = `#fff2e8`
export const volcano2 = `#ffd8bf`
export const volcano3 = `#ffbb96`
export const volcano4 = `#ff9c6e`
export const volcano5 = `#ff7a45`
export const volcano6 = `#fa541c`
export const volcano7 = `#d4380d`
export const volcano8 = `#ad2102`
export const volcano9 = `#871400`
export const volcano10 = `#610b00`
export const geekblue1 = `#f0f5ff`
export const geekblue2 = `#d6e4ff`
export const geekblue3 = `#adc6ff`
export const geekblue4 = `#85a5ff`
export const geekblue5 = `#597ef7`
export const geekblue6 = `#2f54eb`
export const geekblue7 = `#1d39c4`
export const geekblue8 = `#10239e`
export const geekblue9 = `#061178`
export const geekblue10 = `#030852`
export const gold1 = `#fffbe6`
export const gold2 = `#fff1b8`
export const gold3 = `#ffe58f`
export const gold4 = `#ffd666`
export const gold5 = `#ffc53d`
export const gold6 = `#faad14`
export const gold7 = `#d48806`
export const gold8 = `#ad6800`
export const gold9 = `#874d00`
export const gold10 = `#613400`
export const lime1 = `#fcffe6`
export const lime2 = `#f4ffb8`
export const lime3 = `#eaff8f`
export const lime4 = `#d3f261`
export const lime5 = `#bae637`
export const lime6 = `#a0d911`
export const lime7 = `#7cb305`
export const lime8 = `#5b8c00`
export const lime9 = `#3f6600`
export const lime10 = `#254000`
export const colorText = `rgba(0,0,0,0.88)`
export const colorTextSecondary = `rgba(0,0,0,0.65)`
export const colorTextTertiary = `rgba(0,0,0,0.45)`
export const colorTextQuaternary = `rgba(0,0,0,0.25)`
export const colorFill = `rgba(0,0,0,0.15)`
export const colorFillSecondary = `rgba(0,0,0,0.06)`
export const colorFillTertiary = `rgba(0,0,0,0.04)`
export const colorFillQuaternary = `rgba(0,0,0,0.02)`
export const colorBgSolid = `rgb(0,0,0)`
export const colorBgSolidHover = `rgba(0,0,0,0.75)`
export const colorBgSolidActive = `rgba(0,0,0,0.95)`
export const colorBgLayout = `#f5f5f5`
export const colorBgElevated = `#ffffff`
export const colorBgSpotlight = `rgba(0,0,0,0.85)`
export const colorBgBlur = `transparent`
export const colorBorderSecondary = `#f0f0f0`
export const colorPrimaryBg = `#f0fcff`
export const colorPrimaryBgHover = `#f0fcff`
export const colorPrimaryBorder = `#e0f7ff`
export const colorPrimaryBorderHover = `#b8ebff`
export const colorPrimaryHover = `#8dd9fc`
export const colorPrimaryActive = `#4797c9`
export const colorPrimaryTextHover = `#8dd9fc`
export const colorPrimaryText = `#60beef`
export const colorPrimaryTextActive = `#4797c9`
export const colorSuccessBg = `#f6ffed`
export const colorSuccessBgHover = `#d9f7be`
export const colorSuccessBorder = `#b7eb8f`
export const colorSuccessBorderHover = `#95de64`
export const colorSuccessHover = `#95de64`
export const colorSuccessActive = `#389e0d`
export const colorSuccessTextHover = `#73d13d`
export const colorSuccessText = `#52c41a`
export const colorSuccessTextActive = `#389e0d`
export const colorErrorBg = `#fff2f0`
export const colorErrorBgHover = `#fff1f0`
export const colorErrorBgFilledHover = `#ffdfdc`
export const colorErrorBgActive = `#ffccc7`
export const colorErrorBorder = `#ffccc7`
export const colorErrorBorderHover = `#ffa39e`
export const colorErrorHover = `#ff7875`
export const colorErrorActive = `#d9363e`
export const colorErrorTextHover = `#ff7875`
export const colorErrorText = `#ff4d4f`
export const colorErrorTextActive = `#d9363e`
export const colorWarningBg = `#fffbe6`
export const colorWarningBgHover = `#fff1b8`
export const colorWarningBorder = `#ffe58f`
export const colorWarningBorderHover = `#ffd666`
export const colorWarningHover = `#ffd666`
export const colorWarningActive = `#d48806`
export const colorWarningTextHover = `#ffc53d`
export const colorWarningText = `#faad14`
export const colorWarningTextActive = `#d48806`
export const colorInfoBg = `#e6f4ff`
export const colorInfoBgHover = `#bae0ff`
export const colorInfoBorder = `#91caff`
export const colorInfoBorderHover = `#69b1ff`
export const colorInfoHover = `#69b1ff`
export const colorInfoActive = `#0958d9`
export const colorInfoTextHover = `#4096ff`
export const colorInfoText = `#1677ff`
export const colorInfoTextActive = `#0958d9`
export const colorLinkHover = `#69b1ff`
export const colorLinkActive = `#0958d9`
export const colorBgMask = `rgba(0,0,0,0.45)`
export const colorWhite = `#fff`
export const fontSizeSM = `12`
export const fontSizeLG = `16`
export const fontSizeXL = `20`
export const lineHeight = `1.5714285714285714`
export const lineHeightLG = `1.5`
export const lineHeightSM = `1.6666666666666667`
export const fontHeight = `22`
export const fontHeightLG = `24`
export const fontHeightSM = `20`
export const sizeXXL = `48`
export const sizeXL = `32`
export const sizeLG = `24`
export const sizeMD = `20`
export const sizeMS = `16`
export const size = `16`
export const sizeSM = `12`
export const sizeXS = `8`
export const sizeXXS = `4`
export const controlHeightSM = `24`
export const controlHeightXS = `16`
export const controlHeightLG = `40`
export const motionDurationFast = `0.1s`
export const motionDurationMid = `0.2s`
export const motionDurationSlow = `0.3s`
export const lineWidthBold = `2`
export const borderRadiusXS = `2`
export const borderRadiusSM = `4`
export const borderRadiusLG = `8`
export const borderRadiusOuter = `4`
export const colorFillContent = `rgba(0,0,0,0.06)`
export const colorFillContentHover = `rgba(0,0,0,0.15)`
export const colorFillAlter = `rgba(0,0,0,0.02)`
export const colorBgContainerDisabled = `rgba(0,0,0,0.04)`
export const colorBorderBg = `#fafafa`
export const colorSplit = `rgba(0,0,0,0.04)`
export const colorTextPlaceholder = `rgba(0,0,0,0.25)`
export const colorTextDisabled = `rgba(0,0,0,0.25)`
export const colorTextHeading = `rgba(0,0,0,0.88)`
export const colorTextLabel = `rgba(0,0,0,0.65)`
export const colorTextDescription = `rgba(0,0,0,0.45)`
export const colorTextLightSolid = `#fff`
export const colorHighlight = `#ff4d4f`
export const colorBgTextHover = `rgba(0,0,0,0.06)`
export const colorBgTextActive = `rgba(0,0,0,0.15)`
export const colorIcon = `rgba(0,0,0,0.45)`
export const colorIconHover = `rgba(0,0,0,0.88)`
export const colorErrorOutline = `rgba(255,241,239,0.91)`
export const colorWarningOutline = `rgba(255,251,228,0.91)`
export const fontSizeIcon = `12`
export const lineWidthFocus = `3`
export const controlOutlineWidth = `2`
export const controlInteractiveSize = `16`
export const controlItemBgHover = `rgba(0,0,0,0.04)`
export const controlItemBgActive = `#f0fcff`
export const controlItemBgActiveHover = `#f0fcff`
export const controlItemBgActiveDisabled = `rgba(0,0,0,0.15)`
export const controlTmpOutline = `rgba(0,0,0,0.02)`
export const controlOutline = `rgba(239,252,255,0.91)`
export const fontWeightStrong = `600`
export const opacityLoading = `0.65`
export const linkDecoration = `none`
export const linkHoverDecoration = `none`
export const linkFocusDecoration = `none`
export const controlPaddingHorizontal = `12`
export const controlPaddingHorizontalSM = `8`
export const paddingXXS = `4`
export const paddingXS = `8`
export const paddingSM = `12`
export const padding = `16`
export const paddingMD = `20`
export const paddingLG = `24`
export const paddingXL = `32`
export const paddingContentHorizontalLG = `24`
export const paddingContentVerticalLG = `16`
export const paddingContentHorizontal = `16`
export const paddingContentVertical = `12`
export const paddingContentHorizontalSM = `16`
export const paddingContentVerticalSM = `8`
export const marginXXS = `4`
export const marginXS = `8`
export const marginSM = `12`
export const margin = `16`
export const marginMD = `20`
export const marginLG = `24`
export const marginXL = `32`
export const marginXXL = `48`
export const boxShadow = `
      0 6px 16px 0 rgba(0, 0, 0, 0.08),
      0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 9px 28px 8px rgba(0, 0, 0, 0.05)
    `
export const boxShadowSecondary = `
      0 6px 16px 0 rgba(0, 0, 0, 0.08),
      0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 9px 28px 8px rgba(0, 0, 0, 0.05)
    `
export const boxShadowTertiary = `
      0 1px 2px 0 rgba(0, 0, 0, 0.03),
      0 1px 6px -1px rgba(0, 0, 0, 0.02),
      0 2px 4px 0 rgba(0, 0, 0, 0.02)
    `
export const screenXS = `480`
export const screenXSMin = `480`
export const screenXSMax = `575`
export const screenSM = `576`
export const screenSMMin = `576`
export const screenSMMax = `767`
export const screenMD = `768`
export const screenMDMin = `768`
export const screenMDMax = `991`
export const screenLG = `992`
export const screenLGMin = `992`
export const screenLGMax = `1199`
export const screenXL = `1200`
export const screenXLMin = `1200`
export const screenXLMax = `1599`
export const screenXXL = `1600`
export const screenXXLMin = `1600`
export const boxShadowPopoverArrow = `2px 2px 5px rgba(0, 0, 0, 0.05)`
export const boxShadowCard = `
      0 1px 2px -2px rgba(0,0,0,0.16),
      0 3px 6px 0 rgba(0,0,0,0.12),
      0 5px 12px 4px rgba(0,0,0,0.09)
    `
export const boxShadowDrawerRight = `
      -6px 0 16px 0 rgba(0, 0, 0, 0.08),
      -3px 0 6px -4px rgba(0, 0, 0, 0.12),
      -9px 0 28px 8px rgba(0, 0, 0, 0.05)
    `
export const boxShadowDrawerLeft = `
      6px 0 16px 0 rgba(0, 0, 0, 0.08),
      3px 0 6px -4px rgba(0, 0, 0, 0.12),
      9px 0 28px 8px rgba(0, 0, 0, 0.05)
    `
export const boxShadowDrawerUp = `
      0 6px 16px 0 rgba(0, 0, 0, 0.08),
      0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 9px 28px 8px rgba(0, 0, 0, 0.05)
    `
export const boxShadowDrawerDown = `
      0 -6px 16px 0 rgba(0, 0, 0, 0.08),
      0 -3px 6px -4px rgba(0, 0, 0, 0.12),
      0 -9px 28px 8px rgba(0, 0, 0, 0.05)
    `
export const boxShadowTabsOverflowLeft = `inset 10px 0 8px -8px rgba(0, 0, 0, 0.08)`
export const boxShadowTabsOverflowRight = `inset -10px 0 8px -8px rgba(0, 0, 0, 0.08)`
export const boxShadowTabsOverflowTop = `inset 0 10px 8px -8px rgba(0, 0, 0, 0.08)`
export const boxShadowTabsOverflowBottom = `inset 0 -10px 8px -8px rgba(0, 0, 0, 0.08)`

// dark
export const colorPrimaryDark = `#55a5ce`
export const colorSuccessDark = `#49aa19`
export const colorWarningDark = `#d89614`
export const colorErrorDark = `#dc4446`
export const colorInfoDark = `#1668dc`
export const colorLinkDark = `#1668dc`
export const colorTextBaseDark = `#fff`
export const colorBgBaseDark = `#000`
export const colorBgContainerDark = `#333b40`
export const colorBorderDark = `#222222`
export const blue1Dark = `#111a2c`
export const blue2Dark = `#112545`
export const blue3Dark = `#15325b`
export const blue4Dark = `#15417e`
export const blue5Dark = `#1554ad`
export const blue6Dark = `#1668dc`
export const blue7Dark = `#3c89e8`
export const blue8Dark = `#65a9f3`
export const blue9Dark = `#8dc5f8`
export const blue10Dark = `#b7dcfa`
export const purple1Dark = `#1a1325`
export const purple2Dark = `#24163a`
export const purple3Dark = `#301c4d`
export const purple4Dark = `#3e2069`
export const purple5Dark = `#51258f`
export const purple6Dark = `#642ab5`
export const purple7Dark = `#854eca`
export const purple8Dark = `#ab7ae0`
export const purple9Dark = `#cda8f0`
export const purple10Dark = `#ebd7fa`
export const cyan1Dark = `#112123`
export const cyan2Dark = `#113536`
export const cyan3Dark = `#144848`
export const cyan4Dark = `#146262`
export const cyan5Dark = `#138585`
export const cyan6Dark = `#13a8a8`
export const cyan7Dark = `#33bcb7`
export const cyan8Dark = `#58d1c9`
export const cyan9Dark = `#84e2d8`
export const cyan10Dark = `#b2f1e8`
export const green1Dark = `#162312`
export const green2Dark = `#1d3712`
export const green3Dark = `#274916`
export const green4Dark = `#306317`
export const green5Dark = `#3c8618`
export const green6Dark = `#49aa19`
export const green7Dark = `#6abe39`
export const green8Dark = `#8fd460`
export const green9Dark = `#b2e58b`
export const green10Dark = `#d5f2bb`
export const magenta1Dark = `#291321`
export const magenta2Dark = `#40162f`
export const magenta3Dark = `#551c3b`
export const magenta4Dark = `#75204f`
export const magenta5Dark = `#a02669`
export const magenta6Dark = `#cb2b83`
export const magenta7Dark = `#e0529c`
export const magenta8Dark = `#f37fb7`
export const magenta9Dark = `#f8a8cc`
export const magenta10Dark = `#fad2e3`
export const pink1Dark = `#291321`
export const pink2Dark = `#40162f`
export const pink3Dark = `#551c3b`
export const pink4Dark = `#75204f`
export const pink5Dark = `#a02669`
export const pink6Dark = `#cb2b83`
export const pink7Dark = `#e0529c`
export const pink8Dark = `#f37fb7`
export const pink9Dark = `#f8a8cc`
export const pink10Dark = `#fad2e3`
export const red1Dark = `#2a1215`
export const red2Dark = `#431418`
export const red3Dark = `#58181c`
export const red4Dark = `#791a1f`
export const red5Dark = `#a61d24`
export const red6Dark = `#d32029`
export const red7Dark = `#e84749`
export const red8Dark = `#f37370`
export const red9Dark = `#f89f9a`
export const red10Dark = `#fac8c3`
export const orange1Dark = `#2b1d11`
export const orange2Dark = `#442a11`
export const orange3Dark = `#593815`
export const orange4Dark = `#7c4a15`
export const orange5Dark = `#aa6215`
export const orange6Dark = `#d87a16`
export const orange7Dark = `#e89a3c`
export const orange8Dark = `#f3b765`
export const orange9Dark = `#f8cf8d`
export const orange10Dark = `#fae3b7`
export const yellow1Dark = `#2b2611`
export const yellow2Dark = `#443b11`
export const yellow3Dark = `#595014`
export const yellow4Dark = `#7c6e14`
export const yellow5Dark = `#aa9514`
export const yellow6Dark = `#d8bd14`
export const yellow7Dark = `#e8d639`
export const yellow8Dark = `#f3ea62`
export const yellow9Dark = `#f8f48b`
export const yellow10Dark = `#fafab5`
export const volcano1Dark = `#2b1611`
export const volcano2Dark = `#441d12`
export const volcano3Dark = `#592716`
export const volcano4Dark = `#7c3118`
export const volcano5Dark = `#aa3e19`
export const volcano6Dark = `#d84a1b`
export const volcano7Dark = `#e87040`
export const volcano8Dark = `#f3956a`
export const volcano9Dark = `#f8b692`
export const volcano10Dark = `#fad4bc`
export const geekblue1Dark = `#131629`
export const geekblue2Dark = `#161d40`
export const geekblue3Dark = `#1c2755`
export const geekblue4Dark = `#203175`
export const geekblue5Dark = `#263ea0`
export const geekblue6Dark = `#2b4acb`
export const geekblue7Dark = `#5273e0`
export const geekblue8Dark = `#7f9ef3`
export const geekblue9Dark = `#a8c1f8`
export const geekblue10Dark = `#d2e0fa`
export const gold1Dark = `#2b2111`
export const gold2Dark = `#443111`
export const gold3Dark = `#594214`
export const gold4Dark = `#7c5914`
export const gold5Dark = `#aa7714`
export const gold6Dark = `#d89614`
export const gold7Dark = `#e8b339`
export const gold8Dark = `#f3cc62`
export const gold9Dark = `#f8df8b`
export const gold10Dark = `#faedb5`
export const lime1Dark = `#1f2611`
export const lime2Dark = `#2e3c10`
export const lime3Dark = `#3e4f13`
export const lime4Dark = `#536d13`
export const lime5Dark = `#6f9412`
export const lime6Dark = `#8bbb11`
export const lime7Dark = `#a9d134`
export const lime8Dark = `#c9e75d`
export const lime9Dark = `#e4f88b`
export const lime10Dark = `#f0fab5`
export const colorTextDark = `rgba(255,255,255,0.85)`
export const colorTextSecondaryDark = `rgba(255,255,255,0.65)`
export const colorTextTertiaryDark = `rgba(255,255,255,0.45)`
export const colorTextQuaternaryDark = `rgba(255,255,255,0.25)`
export const colorFillDark = `rgba(255,255,255,0.18)`
export const colorFillSecondaryDark = `rgba(255,255,255,0.12)`
export const colorFillTertiaryDark = `rgba(255,255,255,0.08)`
export const colorFillQuaternaryDark = `rgba(255,255,255,0.04)`
export const colorBgSolidDark = `rgba(255,255,255,0.95)`
export const colorBgSolidHoverDark = `rgb(255,255,255)`
export const colorBgSolidActiveDark = `rgba(255,255,255,0.9)`
export const colorBgLayoutDark = `#000000`
export const colorBgElevatedDark = `#1f1f1f`
export const colorBgSpotlightDark = `#424242`
export const colorBgBlurDark = `rgba(255,255,255,0.04)`
export const colorBorderSecondaryDark = `#303030`
export const colorPrimaryBgDark = `#182229`
export const colorPrimaryBgHoverDark = `#213541`
export const colorPrimaryBorderDark = `#2b4756`
export const colorPrimaryBorderHoverDark = `#366177`
export const colorPrimaryHoverDark = `#81c5e5`
export const colorPrimaryActiveDark = `#4583a2`
export const colorPrimaryTextHoverDark = `#81c5e5`
export const colorPrimaryTextDark = `#55a5ce`
export const colorPrimaryTextActiveDark = `#4583a2`
export const colorSuccessBgDark = `#162312`
export const colorSuccessBgHoverDark = `#1d3712`
export const colorSuccessBorderDark = `#274916`
export const colorSuccessBorderHoverDark = `#306317`
export const colorSuccessHoverDark = `#306317`
export const colorSuccessActiveDark = `#3c8618`
export const colorSuccessTextHoverDark = `#6abe39`
export const colorSuccessTextDark = `#49aa19`
export const colorSuccessTextActiveDark = `#3c8618`
export const colorErrorBgDark = `#2c1618`
export const colorErrorBgHoverDark = `#451d1f`
export const colorErrorBgFilledHoverDark = `#441e1f`
export const colorErrorBgActiveDark = `#5b2526`
export const colorErrorBorderDark = `#5b2526`
export const colorErrorBorderHoverDark = `#7e2e2f`
export const colorErrorHoverDark = `#e86e6b`
export const colorErrorActiveDark = `#ad393a`
export const colorErrorTextHoverDark = `#e86e6b`
export const colorErrorTextDark = `#dc4446`
export const colorErrorTextActiveDark = `#ad393a`
export const colorWarningBgDark = `#2b2111`
export const colorWarningBgHoverDark = `#443111`
export const colorWarningBorderDark = `#594214`
export const colorWarningBorderHoverDark = `#7c5914`
export const colorWarningHoverDark = `#7c5914`
export const colorWarningActiveDark = `#aa7714`
export const colorWarningTextHoverDark = `#e8b339`
export const colorWarningTextDark = `#d89614`
export const colorWarningTextActiveDark = `#aa7714`
export const colorInfoBgDark = `#111a2c`
export const colorInfoBgHoverDark = `#112545`
export const colorInfoBorderDark = `#15325b`
export const colorInfoBorderHoverDark = `#15417e`
export const colorInfoHoverDark = `#15417e`
export const colorInfoActiveDark = `#1554ad`
export const colorInfoTextHoverDark = `#3c89e8`
export const colorInfoTextDark = `#1668dc`
export const colorInfoTextActiveDark = `#1554ad`
export const colorLinkHoverDark = `#15417e`
export const colorLinkActiveDark = `#1554ad`
export const colorBgMaskDark = `rgba(0,0,0,0.45)`
export const colorWhiteDark = `#fff`
export const colorFillContentDark = `rgba(255,255,255,0.12)`
export const colorFillContentHoverDark = `rgba(255,255,255,0.18)`
export const colorFillAlterDark = `rgba(255,255,255,0.04)`
export const colorBgContainerDisabledDark = `rgba(255,255,255,0.08)`
export const colorBorderBgDark = `#333b40`
export const colorSplitDark = `rgba(39,15,0,0.25)`
export const colorTextPlaceholderDark = `rgba(255,255,255,0.25)`
export const colorTextDisabledDark = `rgba(255,255,255,0.25)`
export const colorTextHeadingDark = `rgba(255,255,255,0.85)`
export const colorTextLabelDark = `rgba(255,255,255,0.65)`
export const colorTextDescriptionDark = `rgba(255,255,255,0.45)`
export const colorTextLightSolidDark = `#fff`
export const colorHighlightDark = `#dc4446`
export const colorBgTextHoverDark = `rgba(255,255,255,0.12)`
export const colorBgTextActiveDark = `rgba(255,255,255,0.18)`
export const colorIconDark = `rgba(255,255,255,0.45)`
export const colorIconHoverDark = `rgba(255,255,255,0.85)`
export const colorErrorOutlineDark = `rgba(40,0,1,0.63)`
export const colorWarningOutlineDark = `rgba(40,23,0,0.73)`
export const boxShadowDark = `
      0 6px 16px 0 rgba(0, 0, 0, 0.08),
      0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 9px 28px 8px rgba(0, 0, 0, 0.05)
    `
export const boxShadowSecondaryDark = `
      0 6px 16px 0 rgba(0, 0, 0, 0.08),
      0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 9px 28px 8px rgba(0, 0, 0, 0.05)
    `
export const boxShadowTertiaryDark = `
      0 1px 2px 0 rgba(0, 0, 0, 0.03),
      0 1px 6px -1px rgba(0, 0, 0, 0.02),
      0 2px 4px 0 rgba(0, 0, 0, 0.02)
    `
export const boxShadowPopoverArrowDark = `2px 2px 5px rgba(0, 0, 0, 0.05)`
export const boxShadowCardDark = `
      0 1px 2px -2px rgba(0,0,0,0.16),
      0 3px 6px 0 rgba(0,0,0,0.12),
      0 5px 12px 4px rgba(0,0,0,0.09)
    `
export const boxShadowDrawerRightDark = `
      -6px 0 16px 0 rgba(0, 0, 0, 0.08),
      -3px 0 6px -4px rgba(0, 0, 0, 0.12),
      -9px 0 28px 8px rgba(0, 0, 0, 0.05)
    `
export const boxShadowDrawerLeftDark = `
      6px 0 16px 0 rgba(0, 0, 0, 0.08),
      3px 0 6px -4px rgba(0, 0, 0, 0.12),
      9px 0 28px 8px rgba(0, 0, 0, 0.05)
    `
export const boxShadowDrawerUpDark = `
      0 6px 16px 0 rgba(0, 0, 0, 0.08),
      0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 9px 28px 8px rgba(0, 0, 0, 0.05)
    `
export const boxShadowDrawerDownDark = `
      0 -6px 16px 0 rgba(0, 0, 0, 0.08),
      0 -3px 6px -4px rgba(0, 0, 0, 0.12),
      0 -9px 28px 8px rgba(0, 0, 0, 0.05)
    `
export const boxShadowTabsOverflowLeftDark = `inset 10px 0 8px -8px rgba(0, 0, 0, 0.08)`
export const boxShadowTabsOverflowRightDark = `inset -10px 0 8px -8px rgba(0, 0, 0, 0.08)`
export const boxShadowTabsOverflowTopDark = `inset 0 10px 8px -8px rgba(0, 0, 0, 0.08)`
export const boxShadowTabsOverflowBottomDark = `inset 0 -10px 8px -8px rgba(0, 0, 0, 0.08)`
