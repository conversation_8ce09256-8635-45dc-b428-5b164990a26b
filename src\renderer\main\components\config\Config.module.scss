@use '../../../theme' as *;

.container {
  position: relative;
  background: var(--color-bg-container);
  border-right: 1px solid var(--color-border);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.config-list {
  width: 100%;
  flex: 1;
  overflow-y: auto;
}

.config {
  height: 28px;
  line-height: 28px;
  padding: 0 #{$padding-x-s}px;
  display: flex;
  cursor: pointer;
  &:hover {
    background-color: var(--color-fill);
    .config-button {
      display: flex;
    }
  }
  &.connected {
    color: var(--color-primary);
  }
  &.selected {
    background-color: var(--color-primary);
    color: #fff;
    .config-icon {
      img {
        filter: brightness(0) saturate(100%) invert(1);
      }
    }
  }
}

.config-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: #{$margin-x-s}px;
  img {
    width: 16px;
    height: 16px;
  }
}

.config-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.config-button {
  display: none;
  align-items: center;
  justify-content: center;
  margin: 0 2px;
  span {
    border-radius: #{$border-radius-x-s}px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    &:hover {
      background-color: var(--color-fill);
    }
  }
}
