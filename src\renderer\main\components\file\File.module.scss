.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: var(--color-bg-container-lighter);
}

.file-list {
  height: 100%;
  :global(.luna-data-grid) {
    border: none !important;
    background: var(--color-bg-container-lighter);
  }
}

:global(.-theme-with-dark-background) {
  .container {
    background: var(--color-bg-container-darker);
  }
  .file-list {
    :global(.luna-data-grid) {
      background-color: var(--color-bg-container-darker);
    }
  }
}

.loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
}

.highlight {
  background: var(--color-primary-bg) !important;
}
