#!/bin/bash

# Netflix 风格轻量播放器部署脚本

set -e

echo "=== Netflix 风格轻量播放器部署 ==="

# 检查是否为 root 用户
if [ "$EUID" -ne 0 ]; then
    echo "请使用 root 用户运行此脚本"
    exit 1
fi

# 创建播放器目录
PLAYER_DIR="/opt/rclone-player"
mkdir -p $PLAYER_DIR
cd $PLAYER_DIR

# 生成随机密码
PLAYER_PASSWORD=$(openssl rand -base64 12)
echo "播放器密码: $PLAYER_PASSWORD"

# 创建 Rclone HTTP 服务配置
tee /etc/systemd/system/rclone-http.service > /dev/null << EOF
[Unit]
Description=Rclone HTTP File Server
After=network.target

[Service]
Type=simple
User=rclone
Group=rclone
WorkingDirectory=/var/lib/rclone
ExecStart=/usr/bin/rclone serve http --config=/etc/rclone/rclone.conf \\
    --addr=127.0.0.1:8080 \\
    --user=player \\
    --pass=$PLAYER_PASSWORD \\
    --dir-cache-time=10m \\
    --vfs-cache-mode=writes \\
    --buffer-size=64M \\
    --log-level=INFO \\
    --log-file=/var/log/rclone/http.log
Restart=always
RestartSec=10
StandardOutput=append:/var/log/rclone/http.log
StandardError=append:/var/log/rclone/http.log

[Install]
WantedBy=multi-user.target
EOF

# 安装 Nginx（轻量 Web 服务器）
echo "安装 Nginx..."
apt update
apt install -y nginx

# 创建播放器配置文件
tee $PLAYER_DIR/config.js > /dev/null << EOF
// 播放器配置
const CONFIG = {
    rcloneUrl: 'http://127.0.0.1:8080',
    auth: btoa('player:$PLAYER_PASSWORD'),
    title: 'Cloud Media Player',
    supportedFormats: {
        video: ['.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv', '.webm', '.m4v'],
        audio: ['.mp3', '.flac', '.wav', '.aac', '.ogg', '.m4a'],
        image: ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']
    }
};
EOF

# 创建 Nginx 配置
tee /etc/nginx/sites-available/rclone-player > /dev/null << 'EOF'
server {
    listen 9000;
    server_name _;
    
    root /opt/rclone-player;
    index index.html;
    
    # 静态文件
    location / {
        try_files \$uri \$uri/ =404;
    }
    
    # 代理到 Rclone HTTP 服务
    location /api/ {
        proxy_pass http://127.0.0.1:8080/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_buffering off;
        proxy_request_buffering off;
        proxy_max_temp_file_size 0;
        
        # 支持大文件流式传输
        proxy_read_timeout 300;
        proxy_connect_timeout 300;
        proxy_send_timeout 300;
    }
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
}
EOF

# 启用站点
ln -sf /etc/nginx/sites-available/rclone-player /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# 创建管理脚本
tee /usr/local/bin/media-player-manage > /dev/null << 'EOF'
#!/bin/bash

case "$1" in
    start)
        systemctl start rclone-http nginx
        echo "媒体播放器已启动"
        echo "访问地址: http://$(curl -s ifconfig.me):9000"
        ;;
    stop)
        systemctl stop rclone-http nginx
        echo "媒体播放器已停止"
        ;;
    restart)
        systemctl restart rclone-http nginx
        echo "媒体播放器已重启"
        ;;
    status)
        echo "=== Rclone HTTP 状态 ==="
        systemctl status rclone-http --no-pager
        echo ""
        echo "=== Nginx 状态 ==="
        systemctl status nginx --no-pager
        ;;
    logs)
        echo "=== Rclone HTTP 日志 ==="
        tail -f /var/log/rclone/http.log
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|logs}"
        exit 1
        ;;
esac
EOF

chmod +x /usr/local/bin/media-player-manage

# 配置防火墙
if command -v iptables &> /dev/null; then
    iptables -A INPUT -p tcp --dport 9000 -j ACCEPT
    echo "防火墙规则已添加（端口 9000）"
fi

# 启用服务
systemctl daemon-reload
systemctl enable rclone-http nginx

echo "=== 部署完成 ==="
echo ""
echo "重要信息："
echo "- 播放器地址: http://$(curl -s ifconfig.me):9000"
echo "- Rclone HTTP 用户名: player"
echo "- Rclone HTTP 密码: $PLAYER_PASSWORD"
echo ""
echo "管理命令："
echo "- 启动: media-player-manage start"
echo "- 停止: media-player-manage stop"
echo "- 重启: media-player-manage restart"
echo "- 状态: media-player-manage status"
echo "- 日志: media-player-manage logs"
echo ""
echo "下一步: 创建播放器界面文件"
