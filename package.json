{"name": "rem", "productName": "REM", "version": "0.7.0", "description": "Rclone desktop app", "main": "dist/main/index.js", "scripts": {"dev": "concurrently \"npm run dev:main\" \"npm run dev:preload\" \"npm run dev:renderer\"", "dev:main": "vite build --watch --config vite.main.ts --mode=development", "dev:preload": "vite build --watch --config vite.preload.ts --mode=development", "dev:renderer": "vite", "build:main": "vite build --config vite.main.ts", "build:preload": "vite build --config vite.preload.ts", "build:renderer": "vite build", "build": "zx script/build.mjs", "pack": "zx script/pack.mjs", "rclone": "zx script/rclone.mjs", "start": "electron ./dist/main/index.js", "lint": "eslint \"src/**/*.{ts,tsx}\"", "format": "lsla prettier \"src/**/*.{ts,tsx,scss,css,json}\" \"*.{js,ts,json}\" \"script/*.mjs\" --write", "genIcon": "lsla genIcon --input src/renderer/ --output src/renderer/icon.css --name rem-icon --source src/share/renderer/icon/", "genTheme": "lsla genTheme --input src/common/theme.json --output src/renderer/theme.scss && lsla genTheme --input src/common/theme.json --output src/common/theme.ts"}, "repository": {"type": "git", "url": "git+https://github.com/liriliri/rem.git"}, "keywords": ["rclone"], "author": "sur<PERSON><PERSON>", "license": "AGPL-3.0", "bugs": {"url": "https://github.com/liriliri/rem/issues"}, "homepage": "https://github.com/liriliri/rem#readme", "devDependencies": {"@types/node": "^24.0.0", "@types/react": "^19.1.7", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "@xterm/addon-canvas": "^0.7.0", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-unicode11": "^0.8.0", "@xterm/addon-webgl": "^0.18.0", "@xterm/xterm": "^5.5.0", "axios": "^1.10.0", "concurrently": "^9.1.2", "custom-electron-titlebar": "^4.2.8", "electron": "30.5.1", "electron-builder": "^25.1.8", "electron-updater": "^6.6.2", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "fs-extra": "^11.3.0", "globals": "^16.2.0", "licia": "^1.48.0", "luna-data-grid": "^1.6.5", "luna-drag-selector": "^0.1.0", "luna-file-list": "^0.5.4", "luna-icon-list": "^0.2.6", "luna-image-viewer": "^1.1.0", "luna-menu": "^0.1.3", "luna-modal": "^1.3.1", "luna-notification": "^0.3.3", "luna-setting": "^2.0.2", "luna-split-pane": "^0.2.5", "luna-toolbar": "^0.9.2", "luna-video-player": "^1.1.2", "mobx": "^6.13.7", "mobx-react-lite": "^4.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "sass": "^1.89.2", "typescript-eslint": "^8.34.0", "vite": "^5.4.19", "zx": "^8.5.5"}}