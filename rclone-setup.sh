#!/bin/bash

# Rclone VPS 部署脚本
# 适用于 Debian 12 (bookworm)

set -e

echo "=== Rclone VPS 部署脚本 ==="

# 检查是否为 root 用户
if [ "$EUID" -ne 0 ]; then
    echo "请使用 root 用户运行此脚本"
    exit 1
fi

# 更新系统
echo "更新系统包..."
apt update && apt upgrade -y

# 安装必要的工具
echo "安装必要工具..."
apt install -y curl wget unzip fuse3

# 1. 安装 Rclone
echo "正在安装 Rclone..."
curl https://rclone.org/install.sh | bash

# 验证安装
echo "验证 Rclone 安装..."
rclone version

# 2. 创建 rclone 用户和目录
echo "创建 rclone 用户和目录..."
useradd -r -s /bin/false -d /var/lib/rclone rclone || true
mkdir -p /etc/rclone
mkdir -p /var/lib/rclone
mkdir -p /var/log/rclone
chown -R rclone:rclone /var/lib/rclone
chown -R rclone:rclone /var/log/rclone
chown -R rclone:rclone /etc/rclone

# 3. 创建 rclone 配置文件
echo "创建 rclone 配置..."
tee /etc/rclone/rclone.conf > /dev/null << 'EOF'
# Rclone 配置文件
# 请根据需要添加你的云存储配置
# 使用 'rclone config' 命令进行交互式配置
EOF

chown rclone:rclone /etc/rclone/rclone.conf
chmod 600 /etc/rclone/rclone.conf

# 4. 生成随机密码
RCLONE_PASSWORD=$(openssl rand -base64 12)
echo "生成的 Rclone Web UI 密码: $RCLONE_PASSWORD"
echo "请记住此密码，稍后登录时需要使用"

# 5. 创建 systemd 服务文件
echo "创建 systemd 服务..."
tee /etc/systemd/system/rclone-web.service > /dev/null << EOF
[Unit]
Description=Rclone Web UI
After=network.target

[Service]
Type=simple
User=rclone
Group=rclone
WorkingDirectory=/var/lib/rclone
ExecStart=/usr/bin/rclone rcd \\
    --rc-web-gui \\
    --rc-addr=0.0.0.0:5572 \\
    --rc-user=admin \\
    --rc-pass=$RCLONE_PASSWORD \\
    --rc-allow-origin=* \\
    --config=/etc/rclone/rclone.conf \\
    --cache-dir=/var/lib/rclone/cache \\
    --log-level=INFO \\
    --log-file=/var/log/rclone/rclone.log
Restart=always
RestartSec=10
StandardOutput=append:/var/log/rclone/rclone.log
StandardError=append:/var/log/rclone/rclone.log

[Install]
WantedBy=multi-user.target
EOF

# 6. 设置防火墙
echo "配置防火墙..."
if command -v ufw &> /dev/null; then
    ufw allow 5572/tcp
    echo "UFW 防火墙规则已添加"
elif command -v iptables &> /dev/null; then
    iptables -A INPUT -p tcp --dport 5572 -j ACCEPT
    echo "iptables 防火墙规则已添加"
fi

# 7. 启动服务
echo "启动 Rclone 服务..."
systemctl daemon-reload
systemctl enable rclone-web.service

# 8. 创建管理脚本
echo "创建管理脚本..."
tee /usr/local/bin/rclone-manage > /dev/null << 'EOF'
#!/bin/bash

case "$1" in
    start)
        systemctl start rclone-web
        echo "Rclone Web UI 已启动"
        ;;
    stop)
        systemctl stop rclone-web
        echo "Rclone Web UI 已停止"
        ;;
    restart)
        systemctl restart rclone-web
        echo "Rclone Web UI 已重启"
        ;;
    status)
        systemctl status rclone-web
        ;;
    logs)
        tail -f /var/log/rclone/rclone.log
        ;;
    config)
        sudo -u rclone rclone config --config=/etc/rclone/rclone.conf
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|logs|config}"
        exit 1
        ;;
esac
EOF

chmod +x /usr/local/bin/rclone-manage

echo "=== 部署完成 ==="
echo ""
echo "重要信息："
echo "- Web UI 地址: http://$(curl -s ifconfig.me):5572"
echo "- 用户名: admin"
echo "- 密码: $RCLONE_PASSWORD"
echo ""
echo "管理命令："
echo "- 启动服务: rclone-manage start"
echo "- 停止服务: rclone-manage stop"
echo "- 重启服务: rclone-manage restart"
echo "- 查看状态: rclone-manage status"
echo "- 查看日志: rclone-manage logs"
echo "- 配置云存储: rclone-manage config"
echo ""
echo "现在启动服务: systemctl start rclone-web"
