#!/bin/bash

# Rclone VPS 部署脚本
# 适用于 Debian 12 (bookworm)

set -e

echo "=== Rclone VPS 部署脚本 ==="

# 1. 安装 Rclone
echo "正在安装 Rclone..."
curl https://rclone.org/install.sh | sudo bash

# 2. 创建 rclone 用户和目录
echo "创建 rclone 用户和目录..."
sudo useradd -r -s /bin/false rclone || true
sudo mkdir -p /etc/rclone
sudo mkdir -p /var/log/rclone
sudo chown rclone:rclone /var/log/rclone

# 3. 创建 rclone 配置文件
echo "创建 rclone 配置..."
sudo tee /etc/rclone/rclone.conf > /dev/null << 'EOF'
# Rclone 配置文件
# 请根据需要添加你的云存储配置
# 使用 'rclone config' 命令进行交互式配置
EOF

# 4. 创建 systemd 服务文件
echo "创建 systemd 服务..."
sudo tee /etc/systemd/system/rclone-web.service > /dev/null << 'EOF'
[Unit]
Description=Rclone Web UI
After=network.target

[Service]
Type=simple
User=rclone
Group=rclone
ExecStart=/usr/bin/rclone rcd --rc-web-gui --rc-addr=0.0.0.0:5572 --rc-user=admin --rc-pass=your_password_here --config=/etc/rclone/rclone.conf
Restart=always
RestartSec=10
StandardOutput=append:/var/log/rclone/rclone.log
StandardError=append:/var/log/rclone/rclone.log

[Install]
WantedBy=multi-user.target
EOF

# 5. 设置防火墙（如果使用 ufw）
echo "配置防火墙..."
if command -v ufw &> /dev/null; then
    sudo ufw allow 5572/tcp
fi

# 6. 启动服务
echo "启动 Rclone 服务..."
sudo systemctl daemon-reload
sudo systemctl enable rclone-web.service

echo "=== 部署完成 ==="
echo ""
echo "下一步操作："
echo "1. 编辑 /etc/rclone/rclone.conf 添加你的云存储配置"
echo "2. 修改 /etc/systemd/system/rclone-web.service 中的密码"
echo "3. 启动服务: sudo systemctl start rclone-web"
echo "4. 访问 Web UI: http://your-vps-ip:5572"
echo ""
echo "配置云存储: sudo -u rclone rclone config --config=/etc/rclone/rclone.conf"
