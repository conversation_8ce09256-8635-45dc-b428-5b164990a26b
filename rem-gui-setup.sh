#!/bin/bash

# 在 VPS 上运行 REM GUI 的设置脚本
# 注意：这会消耗较多资源，不推荐用于生产环境

set -e

echo "=== REM GUI VPS 部署脚本 ==="
echo "警告：此方案会消耗较多系统资源"

# 1. 安装桌面环境和 VNC
echo "安装桌面环境..."
sudo apt update
sudo apt install -y xfce4 xfce4-goodies tightvncserver

# 2. 安装 Node.js 和 npm
echo "安装 Node.js..."
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# 3. 安装构建工具
echo "安装构建工具..."
sudo apt install -y build-essential git python3 python3-pip

# 4. 创建 VNC 用户
echo "创建 VNC 用户..."
sudo useradd -m -s /bin/bash remuser || true
sudo usermod -aG sudo remuser

# 5. 设置 VNC 服务
echo "设置 VNC 服务..."
sudo tee /etc/systemd/system/vncserver@.service > /dev/null << 'EOF'
[Unit]
Description=Start TightVNC server at startup
After=syslog.target network.target

[Service]
Type=forking
User=remuser
Group=remuser
WorkingDirectory=/home/<USER>

PIDFile=/home/<USER>/.vnc/%H:%i.pid
ExecStartPre=-/usr/bin/vncserver -kill :%i > /dev/null 2>&1
ExecStart=/usr/bin/vncserver -depth 24 -geometry 1280x800 :%i
ExecStop=/usr/bin/vncserver -kill :%i

[Install]
WantedBy=multi-user.target
EOF

# 6. 创建 REM 安装脚本
echo "创建 REM 安装脚本..."
sudo tee /home/<USER>/install-rem.sh > /dev/null << 'EOF'
#!/bin/bash
cd /home/<USER>
git clone https://github.com/liriliri/rem.git
cd rem
npm install
npm run build
EOF

sudo chmod +x /home/<USER>/install-rem.sh
sudo chown remuser:remuser /home/<USER>/install-rem.sh

# 7. 创建启动脚本
sudo tee /home/<USER>/start-rem.sh > /dev/null << 'EOF'
#!/bin/bash
export DISPLAY=:1
cd /home/<USER>/rem
npm start
EOF

sudo chmod +x /home/<USER>/start-rem.sh
sudo chown remuser:remuser /home/<USER>/start-rem.sh

echo "=== 设置完成 ==="
echo ""
echo "下一步操作："
echo "1. 切换到 remuser: sudo su - remuser"
echo "2. 设置 VNC 密码: vncpasswd"
echo "3. 启动 VNC: vncserver :1"
echo "4. 安装 REM: ./install-rem.sh"
echo "5. 启动 REM: ./start-rem.sh"
echo ""
echo "通过 VNC 客户端连接: your-vps-ip:5901"
echo "或使用 SSH X11 转发: ssh -X remuser@your-vps-ip"
