// Netflix 风格播放器 JavaScript

class CloudMediaPlayer {
    constructor() {
        this.currentPath = '';
        this.viewMode = 'grid'; // grid or list
        this.isLoading = false;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadDirectory('');
        this.setupScrollEffect();
    }

    bindEvents() {
        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // 监听键盘事件
        document.addEventListener('keydown', (e) => {
            this.handleKeyboard(e);
        });
    }

    setupScrollEffect() {
        window.addEventListener('scroll', () => {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
    }

    async loadDirectory(path) {
        if (this.isLoading) return;

        this.isLoading = true;
        this.showLoading(true);

        try {
            // 使用标准 URL 编码，Rclone 支持这种格式
            const encodedPath = path.split('/').map(part => encodeURIComponent(part)).join('/');
            // 重要：目录访问必须有尾部斜杠
            const url = `/api/${encodedPath}${encodedPath ? '/' : ''}`;

            console.log('Loading directory:', path, '-> URL:', url);

            const response = await fetch(url);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const html = await response.text();
            const files = this.parseDirectoryListing(html, path);
            this.renderFiles(files);
            this.updateBreadcrumb(path);
            this.currentPath = path;

        } catch (error) {
            console.error('加载目录失败:', error);
            this.showError('加载目录失败: ' + error.message);
        } finally {
            this.isLoading = false;
            this.showLoading(false);
        }
    }

    async tryMultiplePathStrategies(path) {
        const strategies = [
            // 策略1: 不编码（直接传递）
            () => `/api/${path}`,

            // 策略2: 标准 URI 编码
            () => `/api/${path.split('/').map(part => encodeURIComponent(part)).join('/')}`,

            // 策略3: 只编码非 ASCII 字符
            () => `/api/${path.split('/').map(part =>
                part.replace(/[^\x00-\x7F]/g, char => encodeURIComponent(char))
            ).join('/')}`,

            // 策略4: 添加尾部斜杠
            () => `/api/${path}/`,

            // 策略5: 双重编码
            () => `/api/${encodeURIComponent(encodeURIComponent(path))}`,

            // 策略6: 使用原始的 href 值（如果可用）
            () => this.rawHrefMap && this.rawHrefMap[path] ? `/api/${this.rawHrefMap[path]}` : null
        ];

        for (let i = 0; i < strategies.length; i++) {
            try {
                const url = strategies[i]();
                console.log(`尝试策略 ${i + 1}:`, path, '-> URL:', url);

                const response = await fetch(url, {
                    headers: {
                        'Authorization': `Basic ${CONFIG.auth}`
                    }
                });

                if (response.ok) {
                    console.log(`策略 ${i + 1} 成功!`);
                    const html = await response.text();
                    const files = this.parseDirectoryListing(html, path);
                    this.renderFiles(files);
                    this.updateBreadcrumb(path);
                    this.currentPath = path;
                    return true;
                }

                console.log(`策略 ${i + 1} 失败:`, response.status, response.statusText);

            } catch (error) {
                console.log(`策略 ${i + 1} 异常:`, error.message);
            }
        }

        return false;
    }

    parseDirectoryListing(html, currentPath) {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const links = doc.querySelectorAll('a');
        const files = [];

        // 初始化原始 href 映射
        if (!this.rawHrefMap) {
            this.rawHrefMap = {};
        }

        links.forEach(link => {
            const href = link.getAttribute('href');
            const text = link.textContent.trim();

            if (href && text && text !== '../' && text !== './') {
                const isDirectory = href.endsWith('/');

                // 尝试多种方式获取文件名
                let name = text;
                if (isDirectory) {
                    name = text.endsWith('/') ? text.slice(0, -1) : text;
                }

                // 尝试从 href 解码获取更准确的名称
                try {
                    const decodedHref = decodeURIComponent(isDirectory ? href.slice(0, -1) : href);
                    if (decodedHref && decodedHref !== href) {
                        name = decodedHref;
                    }
                } catch (e) {
                    // 解码失败，使用文本内容
                    console.log('解码失败，使用文本:', text);
                }

                const fullPath = currentPath ? `${currentPath}/${name}` : name;

                // 保存原始 href 用于后续请求
                this.rawHrefMap[fullPath] = href;

                files.push({
                    name: name,
                    path: fullPath,
                    rawHref: href,
                    isDirectory: isDirectory,
                    type: this.getFileType(name),
                    size: this.getFileSize(link)
                });
            }
        });

        return files.sort((a, b) => {
            if (a.isDirectory && !b.isDirectory) return -1;
            if (!a.isDirectory && b.isDirectory) return 1;
            return a.name.localeCompare(b.name);
        });
    }

    getFileType(filename) {
        if (!filename.includes('.')) return 'unknown';
        
        const ext = filename.toLowerCase().split('.').pop();
        
        if (CONFIG.supportedFormats.video.some(format => format.includes(ext))) {
            return 'video';
        }
        if (CONFIG.supportedFormats.audio.some(format => format.includes(ext))) {
            return 'audio';
        }
        if (CONFIG.supportedFormats.image.some(format => format.includes(ext))) {
            return 'image';
        }
        
        return 'unknown';
    }

    getFileSize(linkElement) {
        const parent = linkElement.parentElement;
        const text = parent.textContent;
        const sizeMatch = text.match(/(\d+(?:\.\d+)?)\s*(B|KB|MB|GB)/i);
        return sizeMatch ? sizeMatch[0] : '';
    }

    renderFiles(files) {
        const fileGrid = document.getElementById('fileGrid');
        fileGrid.innerHTML = '';

        files.forEach(file => {
            const fileItem = this.createFileItem(file);
            fileGrid.appendChild(fileItem);
        });
    }

    createFileItem(file) {
        const item = document.createElement('div');
        item.className = 'file-item';
        item.onclick = () => this.handleFileClick(file);

        const thumbnail = document.createElement('div');
        thumbnail.className = `file-thumbnail ${file.isDirectory ? 'folder' : file.type}`;

        let icon = 'fas fa-file';
        if (file.isDirectory) {
            icon = 'fas fa-folder';
        } else {
            switch (file.type) {
                case 'video': icon = 'fas fa-play'; break;
                case 'audio': icon = 'fas fa-music'; break;
                case 'image': icon = 'fas fa-image'; break;
                default: icon = 'fas fa-file';
            }
        }

        thumbnail.innerHTML = `<i class="${icon}"></i>`;

        // 添加播放覆盖层
        if (!file.isDirectory && (file.type === 'video' || file.type === 'audio' || file.type === 'image')) {
            const overlay = document.createElement('div');
            overlay.className = 'play-overlay';
            overlay.innerHTML = '<i class="fas fa-play"></i>';
            thumbnail.appendChild(overlay);
        }

        const info = document.createElement('div');
        info.className = 'file-info';
        info.innerHTML = `
            <div class="file-name" title="${file.name}">${file.name}</div>
            <div class="file-size">${file.size}</div>
        `;

        item.appendChild(thumbnail);
        item.appendChild(info);

        return item;
    }

    handleFileClick(file) {
        if (file.isDirectory) {
            this.loadDirectory(file.path);
        } else {
            this.playFile(file);
        }
    }

    playFile(file) {
        // 使用标准 URL 编码
        const encodedPath = file.path.split('/').map(part => encodeURIComponent(part)).join('/');
        const fileUrl = `/api/${encodedPath}`;

        console.log('Playing file:', file.path, '-> URL:', fileUrl);

        switch (file.type) {
            case 'video':
                this.playVideo(fileUrl, file.name);
                break;
            case 'audio':
                this.playAudio(fileUrl, file.name);
                break;
            case 'image':
                this.showImage(fileUrl, file.name);
                break;
            default:
                this.downloadFile(fileUrl, file.name);
        }
    }

    playVideo(url, title) {
        const container = document.getElementById('videoContainer');
        const player = document.getElementById('videoPlayer');
        const titleElement = document.getElementById('videoTitle');

        player.src = url;
        titleElement.textContent = title;
        container.style.display = 'block';
        document.body.style.overflow = 'hidden';

        // 确保控制按钮可见
        const videoInfo = container.querySelector('.video-info');
        if (videoInfo) {
            videoInfo.style.display = 'block';
        }

        // 添加键盘控制
        this.setupVideoControls(player);

        player.play().catch(e => {
            console.error('视频播放失败:', e);
            this.showError('视频播放失败');
        });
    }

    setupVideoControls(player) {
        // 移除之前的事件监听器
        document.removeEventListener('keydown', this.videoKeyHandler);

        // 创建新的键盘事件处理器
        this.videoKeyHandler = (e) => {
            switch(e.key) {
                case ' ':
                case 'k':
                    e.preventDefault();
                    if (player.paused) {
                        player.play();
                    } else {
                        player.pause();
                    }
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    player.currentTime = Math.max(0, player.currentTime - 10);
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    player.currentTime = Math.min(player.duration, player.currentTime + 10);
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    player.volume = Math.min(1, player.volume + 0.1);
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    player.volume = Math.max(0, player.volume - 0.1);
                    break;
                case 'f':
                    e.preventDefault();
                    this.toggleFullscreen();
                    break;
                case 'Escape':
                    e.preventDefault();
                    this.closePlayer();
                    break;
            }
        };

        document.addEventListener('keydown', this.videoKeyHandler);
    }

    playAudio(url, title) {
        const container = document.getElementById('audioContainer');
        const player = document.getElementById('audioPlayer');
        const titleElement = document.getElementById('audioTitle');
        
        player.src = url;
        titleElement.textContent = title;
        container.style.display = 'block';
        document.body.style.overflow = 'hidden';
        
        player.play().catch(e => {
            console.error('音频播放失败:', e);
            this.showError('音频播放失败');
        });
    }

    showImage(url, title) {
        const container = document.getElementById('imageViewer');
        const image = document.getElementById('imageDisplay');
        
        image.src = url;
        image.alt = title;
        container.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }

    downloadFile(url, filename) {
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    }

    updateBreadcrumb(path) {
        const breadcrumb = document.getElementById('breadcrumb');
        const parts = path ? path.split('/') : [];
        
        let html = '<span class="breadcrumb-item" onclick="player.loadDirectory(\'\')">🏠 首页</span>';
        
        let currentPath = '';
        parts.forEach((part, index) => {
            currentPath += (currentPath ? '/' : '') + part;
            const isLast = index === parts.length - 1;
            html += ` / <span class="breadcrumb-item ${isLast ? 'active' : ''}" 
                      onclick="player.loadDirectory('${currentPath}')">${part}</span>`;
        });
        
        breadcrumb.innerHTML = html;
    }

    showLoading(show) {
        const loading = document.getElementById('loading');
        const fileGrid = document.getElementById('fileGrid');
        
        if (show) {
            loading.style.display = 'block';
            fileGrid.style.display = 'none';
        } else {
            loading.style.display = 'none';
            fileGrid.style.display = 'grid';
        }
    }

    showError(message) {
        const errorMessage = document.getElementById('errorMessage');
        const errorText = document.getElementById('errorText');
        
        errorText.textContent = message;
        errorMessage.style.display = 'flex';
    }

    hideError() {
        const errorMessage = document.getElementById('errorMessage');
        errorMessage.style.display = 'none';
    }

    handleKeyboard(e) {
        switch (e.key) {
            case 'Escape':
                this.closePlayer();
                break;
            case 'f':
            case 'F':
                if (e.ctrlKey || e.metaKey) {
                    e.preventDefault();
                    this.toggleFullscreen();
                }
                break;
        }
    }

    handleResize() {
        // 处理窗口大小变化
        const players = ['videoPlayer', 'audioPlayer'];
        players.forEach(id => {
            const player = document.getElementById(id);
            if (player && !player.paused) {
                // 可以在这里添加响应式处理逻辑
            }
        });
    }
}

// 全局函数
function closePlayer() {
    const containers = ['videoContainer', 'audioContainer', 'imageViewer'];
    containers.forEach(id => {
        const container = document.getElementById(id);
        if (container.style.display !== 'none') {
            container.style.display = 'none';
            
            // 停止播放
            const player = container.querySelector('video, audio');
            if (player) {
                player.pause();
                player.src = '';
            }
        }
    });
    
    document.body.style.overflow = 'auto';
}

function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen();
    } else {
        document.exitFullscreen();
    }
}

function toggleView() {
    const icon = document.getElementById('view-icon');
    const fileGrid = document.getElementById('fileGrid');
    
    if (player.viewMode === 'grid') {
        player.viewMode = 'list';
        icon.className = 'fas fa-list';
        fileGrid.style.gridTemplateColumns = '1fr';
    } else {
        player.viewMode = 'grid';
        icon.className = 'fas fa-th-large';
        fileGrid.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';
    }
}

function goHome() {
    player.loadDirectory('');
}

function hideError() {
    player.hideError();
}

function togglePlayPause() {
    const video = document.getElementById('videoPlayer');
    const audio = document.getElementById('audioPlayer');
    const icon = document.getElementById('playPauseIcon');

    if (video && video.style.display !== 'none') {
        if (video.paused) {
            video.play();
            if (icon) icon.className = 'fas fa-pause';
        } else {
            video.pause();
            if (icon) icon.className = 'fas fa-play';
        }
    } else if (audio && audio.style.display !== 'none') {
        if (audio.paused) {
            audio.play();
            if (icon) icon.className = 'fas fa-pause';
        } else {
            audio.pause();
            if (icon) icon.className = 'fas fa-play';
        }
    }
}

// 初始化播放器
const player = new CloudMediaPlayer();
