// Netflix 风格播放器 JavaScript

class CloudMediaPlayer {
    constructor() {
        this.currentPath = '';
        this.viewMode = 'grid'; // grid or list
        this.isLoading = false;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadDirectory('');
        this.setupScrollEffect();
    }

    bindEvents() {
        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // 监听键盘事件
        document.addEventListener('keydown', (e) => {
            this.handleKeyboard(e);
        });
    }

    setupScrollEffect() {
        window.addEventListener('scroll', () => {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
    }

    async loadDirectory(path) {
        if (this.isLoading) return;

        this.isLoading = true;
        this.showLoading(true);

        try {
            // 正确编码路径，但保持斜杠不被编码
            const encodedPath = path.split('/').map(part => encodeURIComponent(part)).join('/');
            const url = `/api/${encodedPath}`;

            console.log('Loading directory:', path, '-> URL:', url);

            const response = await fetch(url, {
                headers: {
                    'Authorization': `Basic ${CONFIG.auth}`
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const html = await response.text();
            const files = this.parseDirectoryListing(html, path);
            this.renderFiles(files);
            this.updateBreadcrumb(path);
            this.currentPath = path;

        } catch (error) {
            console.error('加载目录失败:', error);
            this.showError('加载目录失败: ' + error.message);
        } finally {
            this.isLoading = false;
            this.showLoading(false);
        }
    }

    parseDirectoryListing(html, currentPath) {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const links = doc.querySelectorAll('a');
        const files = [];

        links.forEach(link => {
            const href = link.getAttribute('href');
            const text = link.textContent.trim();

            if (href && text && text !== '../' && text !== './') {
                const isDirectory = href.endsWith('/');
                // 解码 href 以获取正确的文件名
                let name;
                try {
                    name = decodeURIComponent(isDirectory ? href.slice(0, -1) : href);
                } catch (e) {
                    // 如果解码失败，使用原始文本
                    name = isDirectory ? text.slice(0, -1) : text;
                }

                const fullPath = currentPath ? `${currentPath}/${name}` : name;

                files.push({
                    name: name,
                    path: fullPath,
                    isDirectory: isDirectory,
                    type: this.getFileType(name),
                    size: this.getFileSize(link)
                });
            }
        });

        return files.sort((a, b) => {
            if (a.isDirectory && !b.isDirectory) return -1;
            if (!a.isDirectory && b.isDirectory) return 1;
            return a.name.localeCompare(b.name);
        });
    }

    getFileType(filename) {
        if (!filename.includes('.')) return 'unknown';
        
        const ext = filename.toLowerCase().split('.').pop();
        
        if (CONFIG.supportedFormats.video.some(format => format.includes(ext))) {
            return 'video';
        }
        if (CONFIG.supportedFormats.audio.some(format => format.includes(ext))) {
            return 'audio';
        }
        if (CONFIG.supportedFormats.image.some(format => format.includes(ext))) {
            return 'image';
        }
        
        return 'unknown';
    }

    getFileSize(linkElement) {
        const parent = linkElement.parentElement;
        const text = parent.textContent;
        const sizeMatch = text.match(/(\d+(?:\.\d+)?)\s*(B|KB|MB|GB)/i);
        return sizeMatch ? sizeMatch[0] : '';
    }

    renderFiles(files) {
        const fileGrid = document.getElementById('fileGrid');
        fileGrid.innerHTML = '';

        files.forEach(file => {
            const fileItem = this.createFileItem(file);
            fileGrid.appendChild(fileItem);
        });
    }

    createFileItem(file) {
        const item = document.createElement('div');
        item.className = 'file-item';
        item.onclick = () => this.handleFileClick(file);

        const thumbnail = document.createElement('div');
        thumbnail.className = `file-thumbnail ${file.isDirectory ? 'folder' : file.type}`;

        let icon = 'fas fa-file';
        if (file.isDirectory) {
            icon = 'fas fa-folder';
        } else {
            switch (file.type) {
                case 'video': icon = 'fas fa-play'; break;
                case 'audio': icon = 'fas fa-music'; break;
                case 'image': icon = 'fas fa-image'; break;
                default: icon = 'fas fa-file';
            }
        }

        thumbnail.innerHTML = `<i class="${icon}"></i>`;

        // 添加播放覆盖层
        if (!file.isDirectory && (file.type === 'video' || file.type === 'audio' || file.type === 'image')) {
            const overlay = document.createElement('div');
            overlay.className = 'play-overlay';
            overlay.innerHTML = '<i class="fas fa-play"></i>';
            thumbnail.appendChild(overlay);
        }

        const info = document.createElement('div');
        info.className = 'file-info';
        info.innerHTML = `
            <div class="file-name" title="${file.name}">${file.name}</div>
            <div class="file-size">${file.size}</div>
        `;

        item.appendChild(thumbnail);
        item.appendChild(info);

        return item;
    }

    handleFileClick(file) {
        if (file.isDirectory) {
            this.loadDirectory(file.path);
        } else {
            this.playFile(file);
        }
    }

    playFile(file) {
        // 正确编码文件路径
        const encodedPath = file.path.split('/').map(part => encodeURIComponent(part)).join('/');
        const fileUrl = `/api/${encodedPath}`;

        console.log('Playing file:', file.path, '-> URL:', fileUrl);

        switch (file.type) {
            case 'video':
                this.playVideo(fileUrl, file.name);
                break;
            case 'audio':
                this.playAudio(fileUrl, file.name);
                break;
            case 'image':
                this.showImage(fileUrl, file.name);
                break;
            default:
                this.downloadFile(fileUrl, file.name);
        }
    }

    playVideo(url, title) {
        const container = document.getElementById('videoContainer');
        const player = document.getElementById('videoPlayer');
        const titleElement = document.getElementById('videoTitle');
        
        player.src = url;
        titleElement.textContent = title;
        container.style.display = 'block';
        document.body.style.overflow = 'hidden';
        
        player.play().catch(e => {
            console.error('视频播放失败:', e);
            this.showError('视频播放失败');
        });
    }

    playAudio(url, title) {
        const container = document.getElementById('audioContainer');
        const player = document.getElementById('audioPlayer');
        const titleElement = document.getElementById('audioTitle');
        
        player.src = url;
        titleElement.textContent = title;
        container.style.display = 'block';
        document.body.style.overflow = 'hidden';
        
        player.play().catch(e => {
            console.error('音频播放失败:', e);
            this.showError('音频播放失败');
        });
    }

    showImage(url, title) {
        const container = document.getElementById('imageViewer');
        const image = document.getElementById('imageDisplay');
        
        image.src = url;
        image.alt = title;
        container.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }

    downloadFile(url, filename) {
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    }

    updateBreadcrumb(path) {
        const breadcrumb = document.getElementById('breadcrumb');
        const parts = path ? path.split('/') : [];
        
        let html = '<span class="breadcrumb-item" onclick="player.loadDirectory(\'\')">🏠 首页</span>';
        
        let currentPath = '';
        parts.forEach((part, index) => {
            currentPath += (currentPath ? '/' : '') + part;
            const isLast = index === parts.length - 1;
            html += ` / <span class="breadcrumb-item ${isLast ? 'active' : ''}" 
                      onclick="player.loadDirectory('${currentPath}')">${part}</span>`;
        });
        
        breadcrumb.innerHTML = html;
    }

    showLoading(show) {
        const loading = document.getElementById('loading');
        const fileGrid = document.getElementById('fileGrid');
        
        if (show) {
            loading.style.display = 'block';
            fileGrid.style.display = 'none';
        } else {
            loading.style.display = 'none';
            fileGrid.style.display = 'grid';
        }
    }

    showError(message) {
        const errorMessage = document.getElementById('errorMessage');
        const errorText = document.getElementById('errorText');
        
        errorText.textContent = message;
        errorMessage.style.display = 'flex';
    }

    hideError() {
        const errorMessage = document.getElementById('errorMessage');
        errorMessage.style.display = 'none';
    }

    handleKeyboard(e) {
        switch (e.key) {
            case 'Escape':
                this.closePlayer();
                break;
            case 'f':
            case 'F':
                if (e.ctrlKey || e.metaKey) {
                    e.preventDefault();
                    this.toggleFullscreen();
                }
                break;
        }
    }

    handleResize() {
        // 处理窗口大小变化
        const players = ['videoPlayer', 'audioPlayer'];
        players.forEach(id => {
            const player = document.getElementById(id);
            if (player && !player.paused) {
                // 可以在这里添加响应式处理逻辑
            }
        });
    }
}

// 全局函数
function closePlayer() {
    const containers = ['videoContainer', 'audioContainer', 'imageViewer'];
    containers.forEach(id => {
        const container = document.getElementById(id);
        if (container.style.display !== 'none') {
            container.style.display = 'none';
            
            // 停止播放
            const player = container.querySelector('video, audio');
            if (player) {
                player.pause();
                player.src = '';
            }
        }
    });
    
    document.body.style.overflow = 'auto';
}

function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen();
    } else {
        document.exitFullscreen();
    }
}

function toggleView() {
    const icon = document.getElementById('view-icon');
    const fileGrid = document.getElementById('fileGrid');
    
    if (player.viewMode === 'grid') {
        player.viewMode = 'list';
        icon.className = 'fas fa-list';
        fileGrid.style.gridTemplateColumns = '1fr';
    } else {
        player.viewMode = 'grid';
        icon.className = 'fas fa-th-large';
        fileGrid.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';
    }
}

function goHome() {
    player.loadDirectory('');
}

function hideError() {
    player.hideError();
}

// 初始化播放器
const player = new CloudMediaPlayer();
