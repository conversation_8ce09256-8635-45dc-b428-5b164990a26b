#!/bin/bash

# VPS 清理脚本 - 关闭应用并清除缓存

set -e

echo "=== VPS 应用清理脚本 ==="

# 检查是否为 root 用户
if [ "$EUID" -ne 0 ]; then
    echo "请使用 root 用户运行此脚本"
    exit 1
fi

echo "警告: 此脚本将关闭所有相关服务并清除缓存数据"
echo "包括:"
echo "- Rclone Web UI (端口 5572)"
echo "- Rclone HTTP 服务 (端口 8080)"
echo "- 媒体播放器 (端口 9000)"
echo "- 相关缓存和日志文件"
echo ""

read -p "确定要继续吗? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "操作已取消"
    exit 0
fi

echo ""
echo "开始清理..."

# 1. 停止所有相关服务
echo "1. 停止服务..."

# 停止 Rclone Web UI
if systemctl is-active --quiet rclone-web 2>/dev/null; then
    echo "  - 停止 Rclone Web UI"
    systemctl stop rclone-web
    systemctl disable rclone-web
else
    echo "  - Rclone Web UI 未运行"
fi

# 停止 Rclone HTTP 服务
if systemctl is-active --quiet rclone-http 2>/dev/null; then
    echo "  - 停止 Rclone HTTP 服务"
    systemctl stop rclone-http
    systemctl disable rclone-http
else
    echo "  - Rclone HTTP 服务未运行"
fi

# 停止 Nginx
if systemctl is-active --quiet nginx 2>/dev/null; then
    echo "  - 停止 Nginx"
    systemctl stop nginx
    systemctl disable nginx
else
    echo "  - Nginx 未运行"
fi

# 2. 清除服务配置文件
echo ""
echo "2. 清除服务配置文件..."

SERVICE_FILES=(
    "/etc/systemd/system/rclone-web.service"
    "/etc/systemd/system/rclone-http.service"
    "/etc/nginx/sites-available/rclone-player"
    "/etc/nginx/sites-enabled/rclone-player"
)

for file in "${SERVICE_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "  - 删除 $file"
        rm -f "$file"
    fi
done

# 重新加载 systemd
systemctl daemon-reload

# 3. 清除应用目录
echo ""
echo "3. 清除应用目录..."

APP_DIRS=(
    "/opt/rclone_ui"
    "/opt/rclone-player"
)

for dir in "${APP_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "  - 删除目录 $dir"
        rm -rf "$dir"
    else
        echo "  - 目录 $dir 不存在"
    fi
done

# 4. 清除缓存和日志
echo ""
echo "4. 清除缓存和日志..."

CACHE_DIRS=(
    "/var/lib/rclone"
    "/var/log/rclone"
    "/var/cache/rclone"
    "/tmp/rclone*"
)

for pattern in "${CACHE_DIRS[@]}"; do
    if ls $pattern 1> /dev/null 2>&1; then
        echo "  - 清除 $pattern"
        rm -rf $pattern
    else
        echo "  - $pattern 不存在"
    fi
done

# 5. 清除 Rclone 配置（可选）
echo ""
echo "5. Rclone 配置处理..."

if [ -d "/etc/rclone" ]; then
    echo "发现 Rclone 配置目录 /etc/rclone"
    read -p "是否要删除 Rclone 配置? 这将删除所有云存储配置 (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "  - 删除 Rclone 配置"
        rm -rf /etc/rclone
    else
        echo "  - 保留 Rclone 配置"
    fi
else
    echo "  - 未发现 Rclone 配置"
fi

# 6. 清除用户账户
echo ""
echo "6. 清除用户账户..."

if id "rclone" &>/dev/null; then
    echo "  - 删除 rclone 用户"
    userdel -r rclone 2>/dev/null || userdel rclone 2>/dev/null || echo "    警告: 无法完全删除用户"
else
    echo "  - rclone 用户不存在"
fi

# 7. 清除管理脚本
echo ""
echo "7. 清除管理脚本..."

SCRIPTS=(
    "/usr/local/bin/rclone-manage"
    "/usr/local/bin/media-player-manage"
)

for script in "${SCRIPTS[@]}"; do
    if [ -f "$script" ]; then
        echo "  - 删除 $script"
        rm -f "$script"
    fi
done

# 8. 清除防火墙规则
echo ""
echo "8. 清除防火墙规则..."

PORTS=(5572 8080 9000)

for port in "${PORTS[@]}"; do
    if command -v iptables &> /dev/null; then
        # 尝试删除 iptables 规则
        iptables -D INPUT -p tcp --dport $port -j ACCEPT 2>/dev/null && echo "  - 删除端口 $port 的 iptables 规则" || true
    fi
    
    if command -v ufw &> /dev/null; then
        # 尝试删除 ufw 规则
        ufw delete allow $port/tcp 2>/dev/null && echo "  - 删除端口 $port 的 ufw 规则" || true
    fi
done

# 9. 检查端口占用
echo ""
echo "9. 检查端口占用..."

for port in "${PORTS[@]}"; do
    if netstat -tulpn 2>/dev/null | grep -q ":$port "; then
        echo "  - 警告: 端口 $port 仍被占用"
        netstat -tulpn | grep ":$port "
    else
        echo "  - 端口 $port 已释放"
    fi
done

# 10. 清理完成
echo ""
echo "=== 清理完成 ==="
echo ""
echo "已清理的内容:"
echo "✓ 停止并禁用所有相关服务"
echo "✓ 删除服务配置文件"
echo "✓ 清除应用目录"
echo "✓ 清除缓存和日志"
echo "✓ 删除管理脚本"
echo "✓ 清除防火墙规则"
echo ""

# 显示剩余相关进程
echo "检查剩余相关进程:"
if pgrep -f "rclone" > /dev/null; then
    echo "警告: 发现剩余的 rclone 进程:"
    pgrep -f "rclone" | xargs ps -p
    echo ""
    read -p "是否要强制终止这些进程? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        pkill -f "rclone"
        echo "已终止剩余进程"
    fi
else
    echo "✓ 没有发现剩余的 rclone 进程"
fi

echo ""
echo "VPS 清理完成! 系统已恢复到部署前的状态。"
echo ""
echo "如果需要重新部署，请重新运行部署脚本。"
