# Rclone Web UI VPS 部署指南

## 系统要求
- Debian 12 (bookworm) 或 Ubuntu 20.04+
- 至少 512MB 内存
- Root 权限

## 详细部署步骤

### 第一步：连接到 VPS
```bash
ssh root@your-vps-ip
```

### 第二步：下载部署脚本
```bash
# 方法1：直接创建脚本文件
cat > rclone-setup.sh << 'EOF'
# 将 rclone-setup.sh 的内容复制到这里
EOF

# 方法2：如果有 wget 或 curl，可以从网络下载
# wget https://your-domain.com/rclone-setup.sh
```

### 第三步：运行部署脚本
```bash
# 给脚本执行权限
chmod +x rclone-setup.sh

# 运行部署脚本
./rclone-setup.sh
```

### 第四步：启动服务
```bash
# 启动 Rclone Web UI 服务
systemctl start rclone-web

# 检查服务状态
systemctl status rclone-web
```

### 第五步：配置云存储
```bash
# 使用管理脚本配置云存储
rclone-manage config

# 或者直接使用 rclone 命令
sudo -u rclone rclone config --config=/etc/rclone/rclone.conf
```

### 第六步：访问 Web UI
1. 打开浏览器
2. 访问：`http://your-vps-ip:5572`
3. 使用用户名 `admin` 和脚本生成的密码登录

## 常用管理命令

```bash
# 启动服务
rclone-manage start

# 停止服务
rclone-manage stop

# 重启服务
rclone-manage restart

# 查看服务状态
rclone-manage status

# 查看实时日志
rclone-manage logs

# 配置云存储
rclone-manage config
```

## 云存储配置示例

### 配置阿里云 OSS
```bash
rclone-manage config
# 选择 n) New remote
# 输入名称，如：aliyun-oss
# 选择存储类型：5) Alibaba Cloud Object Storage System (OSS)
# 按提示输入 Access Key ID 和 Secret Access Key
```

### 配置腾讯云 COS
```bash
rclone-manage config
# 选择 n) New remote
# 输入名称，如：tencent-cos
# 选择存储类型：4) Tencent Cloud Object Storage (COS)
# 按提示输入相关信息
```

### 配置 Google Drive
```bash
rclone-manage config
# 选择 n) New remote
# 输入名称，如：gdrive
# 选择存储类型：15) Google Drive
# 按提示完成 OAuth 认证
```

## 安全建议

### 1. 修改默认端口
```bash
# 编辑服务文件
nano /etc/systemd/system/rclone-web.service

# 将 --rc-addr=0.0.0.0:5572 改为其他端口，如 8080
# 然后重启服务
systemctl daemon-reload
systemctl restart rclone-web
```

### 2. 设置 SSL/TLS（推荐）
```bash
# 安装 Nginx
apt install nginx

# 配置反向代理和 SSL
# 详细配置见下方 Nginx 配置示例
```

### 3. 限制访问 IP
```bash
# 使用 iptables 限制访问
iptables -A INPUT -p tcp --dport 5572 -s your-allowed-ip -j ACCEPT
iptables -A INPUT -p tcp --dport 5572 -j DROP
```

## 故障排除

### 服务无法启动
```bash
# 查看详细日志
journalctl -u rclone-web -f

# 检查配置文件
cat /etc/rclone/rclone.conf

# 检查端口占用
netstat -tulpn | grep 5572
```

### 无法访问 Web UI
```bash
# 检查防火墙
ufw status
iptables -L

# 检查服务状态
systemctl status rclone-web

# 检查网络连接
curl -I http://localhost:5572
```

### 内存不足
```bash
# 查看内存使用
free -h

# 如果内存不足，可以创建交换文件
dd if=/dev/zero of=/swapfile bs=1024 count=1048576
chmod 600 /swapfile
mkswap /swapfile
swapon /swapfile
echo '/swapfile swap swap defaults 0 0' >> /etc/fstab
```

## 高级配置

### Nginx 反向代理配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:5572;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 自动备份配置
```bash
# 创建备份脚本
cat > /usr/local/bin/backup-rclone-config << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
cp /etc/rclone/rclone.conf /var/backups/rclone.conf.$DATE
find /var/backups -name "rclone.conf.*" -mtime +7 -delete
EOF

chmod +x /usr/local/bin/backup-rclone-config

# 添加到 crontab
echo "0 2 * * * /usr/local/bin/backup-rclone-config" | crontab -
```
