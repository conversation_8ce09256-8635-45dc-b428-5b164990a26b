<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cloud Media Player</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 顶部导航 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-play-circle"></i>
                <span>Cloud Player</span>
            </div>
            <div class="nav-controls">
                <button class="nav-btn" onclick="toggleView()">
                    <i class="fas fa-th-large" id="view-icon"></i>
                </button>
                <button class="nav-btn" onclick="goHome()">
                    <i class="fas fa-home"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- 主容器 -->
    <div class="main-container">
        <!-- 文件浏览器 -->
        <div class="file-browser" id="fileBrowser">
            <div class="breadcrumb" id="breadcrumb"></div>
            <div class="loading" id="loading">
                <i class="fas fa-spinner fa-spin"></i>
                <span>加载中...</span>
            </div>
            <div class="file-grid" id="fileGrid"></div>
        </div>

        <!-- 视频播放器 -->
        <div class="video-player-container" id="videoContainer" style="display: none;">
            <div class="video-wrapper">
                <video id="videoPlayer" controls preload="metadata">
                    您的浏览器不支持视频播放。
                </video>
                <div class="video-shortcuts">
                    <div><strong>快捷键:</strong></div>
                    <div>空格/K - 播放/暂停</div>
                    <div>← → - 快退/快进 10秒</div>
                    <div>↑ ↓ - 音量 +/-</div>
                    <div>F - 全屏</div>
                    <div>ESC - 退出</div>
                </div>
                <div class="video-info">
                    <h2 id="videoTitle">视频标题</h2>
                    <div class="video-controls">
                        <button class="control-btn" onclick="closePlayer()">
                            <i class="fas fa-times"></i> 关闭
                        </button>
                        <button class="control-btn" onclick="toggleFullscreen()">
                            <i class="fas fa-expand"></i> 全屏
                        </button>
                        <button class="control-btn" onclick="togglePlayPause()">
                            <i class="fas fa-play" id="playPauseIcon"></i> 播放/暂停
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 音频播放器 -->
        <div class="audio-player-container" id="audioContainer" style="display: none;">
            <div class="audio-wrapper">
                <div class="audio-info">
                    <h2 id="audioTitle">音频标题</h2>
                    <div class="audio-cover">
                        <i class="fas fa-music"></i>
                    </div>
                </div>
                <audio id="audioPlayer" controls preload="metadata">
                    您的浏览器不支持音频播放。
                </audio>
                <div class="audio-controls">
                    <button class="control-btn" onclick="closePlayer()">
                        <i class="fas fa-times"></i> 关闭
                    </button>
                </div>
            </div>
        </div>

        <!-- 图片查看器 -->
        <div class="image-viewer" id="imageViewer" style="display: none;">
            <div class="image-container">
                <img id="imageDisplay" alt="图片">
                <div class="image-controls">
                    <button class="control-btn" onclick="closePlayer()">
                        <i class="fas fa-times"></i> 关闭
                    </button>
                    <button class="control-btn" onclick="toggleFullscreen()">
                        <i class="fas fa-expand"></i> 全屏
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 错误提示 -->
    <div class="error-message" id="errorMessage" style="display: none;">
        <div class="error-content">
            <i class="fas fa-exclamation-triangle"></i>
            <span id="errorText">发生错误</span>
            <button onclick="hideError()">确定</button>
        </div>
    </div>

    <script src="config.js"></script>
    <script src="app.js"></script>
</body>
</html>
