{"compilerOptions": {"baseUrl": "src", "target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ES2020", "typeRoots": ["types"], "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "noImplicitAny": false, "jsx": "react-jsx"}, "include": ["src"], "references": [{"path": "./tsconfig.node.json"}]}