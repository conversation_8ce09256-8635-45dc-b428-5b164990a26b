// light
$color-primary: #60beef;
$color-success: #52c41a;
$color-warning: #faad14;
$color-error: #ff4d4f;
$color-info: #1677ff;
$color-link: #1677ff;
$color-text-base: #000;
$color-bg-base: #fff;
$font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
  'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji',
  'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
$font-family-code: 'SFMono-Regular', <PERSON><PERSON><PERSON>, 'Liberation Mono', Menlo, Courier,
  monospace;
$font-size: 14;
$line-width: 1;
$line-type: solid;
$motion-unit: 0.1;
$motion-base: 0;
$motion-ease-out-circ: cubic-bezier(0.08, 0.82, 0.17, 1);
$motion-ease-in-out-circ: cubic-bezier(0.78, 0.14, 0.15, 0.86);
$motion-ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);
$motion-ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);
$motion-ease-out-back: cubic-bezier(0.12, 0.4, 0.29, 1.46);
$motion-ease-in-back: cubic-bezier(0.71, -0.46, 0.88, 0.6);
$motion-ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);
$motion-ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);
$border-radius: 6;
$size-unit: 4;
$size-step: 4;
$size-popup-arrow: 16;
$control-height: 32;
$z-index-base: 0;
$z-index-popup-base: 1000;
$opacity-image: 1;
$wireframe: false;
$motion: true;
$color-bg-container: #fafafa;
$color-border: #dadada;
$blue-1: #e6f4ff;
$blue-2: #bae0ff;
$blue-3: #91caff;
$blue-4: #69b1ff;
$blue-5: #4096ff;
$blue-6: #1677ff;
$blue-7: #0958d9;
$blue-8: #003eb3;
$blue-9: #002c8c;
$blue-10: #001d66;
$purple-1: #f9f0ff;
$purple-2: #efdbff;
$purple-3: #d3adf7;
$purple-4: #b37feb;
$purple-5: #9254de;
$purple-6: #722ed1;
$purple-7: #531dab;
$purple-8: #391085;
$purple-9: #22075e;
$purple-10: #120338;
$cyan-1: #e6fffb;
$cyan-2: #b5f5ec;
$cyan-3: #87e8de;
$cyan-4: #5cdbd3;
$cyan-5: #36cfc9;
$cyan-6: #13c2c2;
$cyan-7: #08979c;
$cyan-8: #006d75;
$cyan-9: #00474f;
$cyan-10: #002329;
$green-1: #f6ffed;
$green-2: #d9f7be;
$green-3: #b7eb8f;
$green-4: #95de64;
$green-5: #73d13d;
$green-6: #52c41a;
$green-7: #389e0d;
$green-8: #237804;
$green-9: #135200;
$green-10: #092b00;
$magenta-1: #fff0f6;
$magenta-2: #ffd6e7;
$magenta-3: #ffadd2;
$magenta-4: #ff85c0;
$magenta-5: #f759ab;
$magenta-6: #eb2f96;
$magenta-7: #c41d7f;
$magenta-8: #9e1068;
$magenta-9: #780650;
$magenta-10: #520339;
$pink-1: #fff0f6;
$pink-2: #ffd6e7;
$pink-3: #ffadd2;
$pink-4: #ff85c0;
$pink-5: #f759ab;
$pink-6: #eb2f96;
$pink-7: #c41d7f;
$pink-8: #9e1068;
$pink-9: #780650;
$pink-10: #520339;
$red-1: #fff1f0;
$red-2: #ffccc7;
$red-3: #ffa39e;
$red-4: #ff7875;
$red-5: #ff4d4f;
$red-6: #f5222d;
$red-7: #cf1322;
$red-8: #a8071a;
$red-9: #820014;
$red-10: #5c0011;
$orange-1: #fff7e6;
$orange-2: #ffe7ba;
$orange-3: #ffd591;
$orange-4: #ffc069;
$orange-5: #ffa940;
$orange-6: #fa8c16;
$orange-7: #d46b08;
$orange-8: #ad4e00;
$orange-9: #873800;
$orange-10: #612500;
$yellow-1: #feffe6;
$yellow-2: #ffffb8;
$yellow-3: #fffb8f;
$yellow-4: #fff566;
$yellow-5: #ffec3d;
$yellow-6: #fadb14;
$yellow-7: #d4b106;
$yellow-8: #ad8b00;
$yellow-9: #876800;
$yellow-10: #614700;
$volcano-1: #fff2e8;
$volcano-2: #ffd8bf;
$volcano-3: #ffbb96;
$volcano-4: #ff9c6e;
$volcano-5: #ff7a45;
$volcano-6: #fa541c;
$volcano-7: #d4380d;
$volcano-8: #ad2102;
$volcano-9: #871400;
$volcano-10: #610b00;
$geekblue-1: #f0f5ff;
$geekblue-2: #d6e4ff;
$geekblue-3: #adc6ff;
$geekblue-4: #85a5ff;
$geekblue-5: #597ef7;
$geekblue-6: #2f54eb;
$geekblue-7: #1d39c4;
$geekblue-8: #10239e;
$geekblue-9: #061178;
$geekblue-10: #030852;
$gold-1: #fffbe6;
$gold-2: #fff1b8;
$gold-3: #ffe58f;
$gold-4: #ffd666;
$gold-5: #ffc53d;
$gold-6: #faad14;
$gold-7: #d48806;
$gold-8: #ad6800;
$gold-9: #874d00;
$gold-10: #613400;
$lime-1: #fcffe6;
$lime-2: #f4ffb8;
$lime-3: #eaff8f;
$lime-4: #d3f261;
$lime-5: #bae637;
$lime-6: #a0d911;
$lime-7: #7cb305;
$lime-8: #5b8c00;
$lime-9: #3f6600;
$lime-10: #254000;
$color-text: rgba(0, 0, 0, 0.88);
$color-text-secondary: rgba(0, 0, 0, 0.65);
$color-text-tertiary: rgba(0, 0, 0, 0.45);
$color-text-quaternary: rgba(0, 0, 0, 0.25);
$color-fill: rgba(0, 0, 0, 0.15);
$color-fill-secondary: rgba(0, 0, 0, 0.06);
$color-fill-tertiary: rgba(0, 0, 0, 0.04);
$color-fill-quaternary: rgba(0, 0, 0, 0.02);
$color-bg-solid: rgb(0, 0, 0);
$color-bg-solid-hover: rgba(0, 0, 0, 0.75);
$color-bg-solid-active: rgba(0, 0, 0, 0.95);
$color-bg-layout: #f5f5f5;
$color-bg-elevated: #ffffff;
$color-bg-spotlight: rgba(0, 0, 0, 0.85);
$color-bg-blur: transparent;
$color-border-secondary: #f0f0f0;
$color-primary-bg: #f0fcff;
$color-primary-bg-hover: #f0fcff;
$color-primary-border: #e0f7ff;
$color-primary-border-hover: #b8ebff;
$color-primary-hover: #8dd9fc;
$color-primary-active: #4797c9;
$color-primary-text-hover: #8dd9fc;
$color-primary-text: #60beef;
$color-primary-text-active: #4797c9;
$color-success-bg: #f6ffed;
$color-success-bg-hover: #d9f7be;
$color-success-border: #b7eb8f;
$color-success-border-hover: #95de64;
$color-success-hover: #95de64;
$color-success-active: #389e0d;
$color-success-text-hover: #73d13d;
$color-success-text: #52c41a;
$color-success-text-active: #389e0d;
$color-error-bg: #fff2f0;
$color-error-bg-hover: #fff1f0;
$color-error-bg-filled-hover: #ffdfdc;
$color-error-bg-active: #ffccc7;
$color-error-border: #ffccc7;
$color-error-border-hover: #ffa39e;
$color-error-hover: #ff7875;
$color-error-active: #d9363e;
$color-error-text-hover: #ff7875;
$color-error-text: #ff4d4f;
$color-error-text-active: #d9363e;
$color-warning-bg: #fffbe6;
$color-warning-bg-hover: #fff1b8;
$color-warning-border: #ffe58f;
$color-warning-border-hover: #ffd666;
$color-warning-hover: #ffd666;
$color-warning-active: #d48806;
$color-warning-text-hover: #ffc53d;
$color-warning-text: #faad14;
$color-warning-text-active: #d48806;
$color-info-bg: #e6f4ff;
$color-info-bg-hover: #bae0ff;
$color-info-border: #91caff;
$color-info-border-hover: #69b1ff;
$color-info-hover: #69b1ff;
$color-info-active: #0958d9;
$color-info-text-hover: #4096ff;
$color-info-text: #1677ff;
$color-info-text-active: #0958d9;
$color-link-hover: #69b1ff;
$color-link-active: #0958d9;
$color-bg-mask: rgba(0, 0, 0, 0.45);
$color-white: #fff;
$font-size-s-m: 12;
$font-size-l-g: 16;
$font-size-x-l: 20;
$line-height: 1.5714285714285714;
$line-height-l-g: 1.5;
$line-height-s-m: 1.6666666666666667;
$font-height: 22;
$font-height-l-g: 24;
$font-height-s-m: 20;
$size-x-x-l: 48;
$size-x-l: 32;
$size-l-g: 24;
$size-m-d: 20;
$size-m-s: 16;
$size: 16;
$size-s-m: 12;
$size-x-s: 8;
$size-x-x-s: 4;
$control-height-s-m: 24;
$control-height-x-s: 16;
$control-height-l-g: 40;
$motion-duration-fast: 0.1s;
$motion-duration-mid: 0.2s;
$motion-duration-slow: 0.3s;
$line-width-bold: 2;
$border-radius-x-s: 2;
$border-radius-s-m: 4;
$border-radius-l-g: 8;
$border-radius-outer: 4;
$color-fill-content: rgba(0, 0, 0, 0.06);
$color-fill-content-hover: rgba(0, 0, 0, 0.15);
$color-fill-alter: rgba(0, 0, 0, 0.02);
$color-bg-container-disabled: rgba(0, 0, 0, 0.04);
$color-border-bg: #fafafa;
$color-split: rgba(0, 0, 0, 0.04);
$color-text-placeholder: rgba(0, 0, 0, 0.25);
$color-text-disabled: rgba(0, 0, 0, 0.25);
$color-text-heading: rgba(0, 0, 0, 0.88);
$color-text-label: rgba(0, 0, 0, 0.65);
$color-text-description: rgba(0, 0, 0, 0.45);
$color-text-light-solid: #fff;
$color-highlight: #ff4d4f;
$color-bg-text-hover: rgba(0, 0, 0, 0.06);
$color-bg-text-active: rgba(0, 0, 0, 0.15);
$color-icon: rgba(0, 0, 0, 0.45);
$color-icon-hover: rgba(0, 0, 0, 0.88);
$color-error-outline: rgba(255, 241, 239, 0.91);
$color-warning-outline: rgba(255, 251, 228, 0.91);
$font-size-icon: 12;
$line-width-focus: 3;
$control-outline-width: 2;
$control-interactive-size: 16;
$control-item-bg-hover: rgba(0, 0, 0, 0.04);
$control-item-bg-active: #f0fcff;
$control-item-bg-active-hover: #f0fcff;
$control-item-bg-active-disabled: rgba(0, 0, 0, 0.15);
$control-tmp-outline: rgba(0, 0, 0, 0.02);
$control-outline: rgba(239, 252, 255, 0.91);
$font-weight-strong: 600;
$opacity-loading: 0.65;
$link-decoration: none;
$link-hover-decoration: none;
$link-focus-decoration: none;
$control-padding-horizontal: 12;
$control-padding-horizontal-s-m: 8;
$padding-x-x-s: 4;
$padding-x-s: 8;
$padding-s-m: 12;
$padding: 16;
$padding-m-d: 20;
$padding-l-g: 24;
$padding-x-l: 32;
$padding-content-horizontal-l-g: 24;
$padding-content-vertical-l-g: 16;
$padding-content-horizontal: 16;
$padding-content-vertical: 12;
$padding-content-horizontal-s-m: 16;
$padding-content-vertical-s-m: 8;
$margin-x-x-s: 4;
$margin-x-s: 8;
$margin-s-m: 12;
$margin: 16;
$margin-m-d: 20;
$margin-l-g: 24;
$margin-x-l: 32;
$margin-x-x-l: 48;
$box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08),
  0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
$box-shadow-secondary: 0 6px 16px 0 rgba(0, 0, 0, 0.08),
  0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
$box-shadow-tertiary: 0 1px 2px 0 rgba(0, 0, 0, 0.03),
  0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
$screen-x-s: 480;
$screen-x-s-min: 480;
$screen-x-s-max: 575;
$screen-s-m: 576;
$screen-s-m-min: 576;
$screen-s-m-max: 767;
$screen-m-d: 768;
$screen-m-d-min: 768;
$screen-m-d-max: 991;
$screen-l-g: 992;
$screen-l-g-min: 992;
$screen-l-g-max: 1199;
$screen-x-l: 1200;
$screen-x-l-min: 1200;
$screen-x-l-max: 1599;
$screen-x-x-l: 1600;
$screen-x-x-l-min: 1600;
$box-shadow-popover-arrow: 2px 2px 5px rgba(0, 0, 0, 0.05);
$box-shadow-card: 0 1px 2px -2px rgba(0, 0, 0, 0.16),
  0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);
$box-shadow-drawer-right: -6px 0 16px 0 rgba(0, 0, 0, 0.08),
  -3px 0 6px -4px rgba(0, 0, 0, 0.12), -9px 0 28px 8px rgba(0, 0, 0, 0.05);
$box-shadow-drawer-left: 6px 0 16px 0 rgba(0, 0, 0, 0.08),
  3px 0 6px -4px rgba(0, 0, 0, 0.12), 9px 0 28px 8px rgba(0, 0, 0, 0.05);
$box-shadow-drawer-up: 0 6px 16px 0 rgba(0, 0, 0, 0.08),
  0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
$box-shadow-drawer-down: 0 -6px 16px 0 rgba(0, 0, 0, 0.08),
  0 -3px 6px -4px rgba(0, 0, 0, 0.12), 0 -9px 28px 8px rgba(0, 0, 0, 0.05);
$box-shadow-tabs-overflow-left: inset 10px 0 8px -8px rgba(0, 0, 0, 0.08);
$box-shadow-tabs-overflow-right: inset -10px 0 8px -8px rgba(0, 0, 0, 0.08);
$box-shadow-tabs-overflow-top: inset 0 10px 8px -8px rgba(0, 0, 0, 0.08);
$box-shadow-tabs-overflow-bottom: inset 0 -10px 8px -8px rgba(0, 0, 0, 0.08);

// dark
$color-primary-dark: #55a5ce;
$color-success-dark: #49aa19;
$color-warning-dark: #d89614;
$color-error-dark: #dc4446;
$color-info-dark: #1668dc;
$color-link-dark: #1668dc;
$color-text-base-dark: #fff;
$color-bg-base-dark: #000;
$color-bg-container-dark: #333b40;
$color-border-dark: #222222;
$blue-1-dark: #111a2c;
$blue-2-dark: #112545;
$blue-3-dark: #15325b;
$blue-4-dark: #15417e;
$blue-5-dark: #1554ad;
$blue-6-dark: #1668dc;
$blue-7-dark: #3c89e8;
$blue-8-dark: #65a9f3;
$blue-9-dark: #8dc5f8;
$blue-10-dark: #b7dcfa;
$purple-1-dark: #1a1325;
$purple-2-dark: #24163a;
$purple-3-dark: #301c4d;
$purple-4-dark: #3e2069;
$purple-5-dark: #51258f;
$purple-6-dark: #642ab5;
$purple-7-dark: #854eca;
$purple-8-dark: #ab7ae0;
$purple-9-dark: #cda8f0;
$purple-10-dark: #ebd7fa;
$cyan-1-dark: #112123;
$cyan-2-dark: #113536;
$cyan-3-dark: #144848;
$cyan-4-dark: #146262;
$cyan-5-dark: #138585;
$cyan-6-dark: #13a8a8;
$cyan-7-dark: #33bcb7;
$cyan-8-dark: #58d1c9;
$cyan-9-dark: #84e2d8;
$cyan-10-dark: #b2f1e8;
$green-1-dark: #162312;
$green-2-dark: #1d3712;
$green-3-dark: #274916;
$green-4-dark: #306317;
$green-5-dark: #3c8618;
$green-6-dark: #49aa19;
$green-7-dark: #6abe39;
$green-8-dark: #8fd460;
$green-9-dark: #b2e58b;
$green-10-dark: #d5f2bb;
$magenta-1-dark: #291321;
$magenta-2-dark: #40162f;
$magenta-3-dark: #551c3b;
$magenta-4-dark: #75204f;
$magenta-5-dark: #a02669;
$magenta-6-dark: #cb2b83;
$magenta-7-dark: #e0529c;
$magenta-8-dark: #f37fb7;
$magenta-9-dark: #f8a8cc;
$magenta-10-dark: #fad2e3;
$pink-1-dark: #291321;
$pink-2-dark: #40162f;
$pink-3-dark: #551c3b;
$pink-4-dark: #75204f;
$pink-5-dark: #a02669;
$pink-6-dark: #cb2b83;
$pink-7-dark: #e0529c;
$pink-8-dark: #f37fb7;
$pink-9-dark: #f8a8cc;
$pink-10-dark: #fad2e3;
$red-1-dark: #2a1215;
$red-2-dark: #431418;
$red-3-dark: #58181c;
$red-4-dark: #791a1f;
$red-5-dark: #a61d24;
$red-6-dark: #d32029;
$red-7-dark: #e84749;
$red-8-dark: #f37370;
$red-9-dark: #f89f9a;
$red-10-dark: #fac8c3;
$orange-1-dark: #2b1d11;
$orange-2-dark: #442a11;
$orange-3-dark: #593815;
$orange-4-dark: #7c4a15;
$orange-5-dark: #aa6215;
$orange-6-dark: #d87a16;
$orange-7-dark: #e89a3c;
$orange-8-dark: #f3b765;
$orange-9-dark: #f8cf8d;
$orange-10-dark: #fae3b7;
$yellow-1-dark: #2b2611;
$yellow-2-dark: #443b11;
$yellow-3-dark: #595014;
$yellow-4-dark: #7c6e14;
$yellow-5-dark: #aa9514;
$yellow-6-dark: #d8bd14;
$yellow-7-dark: #e8d639;
$yellow-8-dark: #f3ea62;
$yellow-9-dark: #f8f48b;
$yellow-10-dark: #fafab5;
$volcano-1-dark: #2b1611;
$volcano-2-dark: #441d12;
$volcano-3-dark: #592716;
$volcano-4-dark: #7c3118;
$volcano-5-dark: #aa3e19;
$volcano-6-dark: #d84a1b;
$volcano-7-dark: #e87040;
$volcano-8-dark: #f3956a;
$volcano-9-dark: #f8b692;
$volcano-10-dark: #fad4bc;
$geekblue-1-dark: #131629;
$geekblue-2-dark: #161d40;
$geekblue-3-dark: #1c2755;
$geekblue-4-dark: #203175;
$geekblue-5-dark: #263ea0;
$geekblue-6-dark: #2b4acb;
$geekblue-7-dark: #5273e0;
$geekblue-8-dark: #7f9ef3;
$geekblue-9-dark: #a8c1f8;
$geekblue-10-dark: #d2e0fa;
$gold-1-dark: #2b2111;
$gold-2-dark: #443111;
$gold-3-dark: #594214;
$gold-4-dark: #7c5914;
$gold-5-dark: #aa7714;
$gold-6-dark: #d89614;
$gold-7-dark: #e8b339;
$gold-8-dark: #f3cc62;
$gold-9-dark: #f8df8b;
$gold-10-dark: #faedb5;
$lime-1-dark: #1f2611;
$lime-2-dark: #2e3c10;
$lime-3-dark: #3e4f13;
$lime-4-dark: #536d13;
$lime-5-dark: #6f9412;
$lime-6-dark: #8bbb11;
$lime-7-dark: #a9d134;
$lime-8-dark: #c9e75d;
$lime-9-dark: #e4f88b;
$lime-10-dark: #f0fab5;
$color-text-dark: rgba(255, 255, 255, 0.85);
$color-text-secondary-dark: rgba(255, 255, 255, 0.65);
$color-text-tertiary-dark: rgba(255, 255, 255, 0.45);
$color-text-quaternary-dark: rgba(255, 255, 255, 0.25);
$color-fill-dark: rgba(255, 255, 255, 0.18);
$color-fill-secondary-dark: rgba(255, 255, 255, 0.12);
$color-fill-tertiary-dark: rgba(255, 255, 255, 0.08);
$color-fill-quaternary-dark: rgba(255, 255, 255, 0.04);
$color-bg-solid-dark: rgba(255, 255, 255, 0.95);
$color-bg-solid-hover-dark: rgb(255, 255, 255);
$color-bg-solid-active-dark: rgba(255, 255, 255, 0.9);
$color-bg-layout-dark: #000000;
$color-bg-elevated-dark: #1f1f1f;
$color-bg-spotlight-dark: #424242;
$color-bg-blur-dark: rgba(255, 255, 255, 0.04);
$color-border-secondary-dark: #303030;
$color-primary-bg-dark: #182229;
$color-primary-bg-hover-dark: #213541;
$color-primary-border-dark: #2b4756;
$color-primary-border-hover-dark: #366177;
$color-primary-hover-dark: #81c5e5;
$color-primary-active-dark: #4583a2;
$color-primary-text-hover-dark: #81c5e5;
$color-primary-text-dark: #55a5ce;
$color-primary-text-active-dark: #4583a2;
$color-success-bg-dark: #162312;
$color-success-bg-hover-dark: #1d3712;
$color-success-border-dark: #274916;
$color-success-border-hover-dark: #306317;
$color-success-hover-dark: #306317;
$color-success-active-dark: #3c8618;
$color-success-text-hover-dark: #6abe39;
$color-success-text-dark: #49aa19;
$color-success-text-active-dark: #3c8618;
$color-error-bg-dark: #2c1618;
$color-error-bg-hover-dark: #451d1f;
$color-error-bg-filled-hover-dark: #441e1f;
$color-error-bg-active-dark: #5b2526;
$color-error-border-dark: #5b2526;
$color-error-border-hover-dark: #7e2e2f;
$color-error-hover-dark: #e86e6b;
$color-error-active-dark: #ad393a;
$color-error-text-hover-dark: #e86e6b;
$color-error-text-dark: #dc4446;
$color-error-text-active-dark: #ad393a;
$color-warning-bg-dark: #2b2111;
$color-warning-bg-hover-dark: #443111;
$color-warning-border-dark: #594214;
$color-warning-border-hover-dark: #7c5914;
$color-warning-hover-dark: #7c5914;
$color-warning-active-dark: #aa7714;
$color-warning-text-hover-dark: #e8b339;
$color-warning-text-dark: #d89614;
$color-warning-text-active-dark: #aa7714;
$color-info-bg-dark: #111a2c;
$color-info-bg-hover-dark: #112545;
$color-info-border-dark: #15325b;
$color-info-border-hover-dark: #15417e;
$color-info-hover-dark: #15417e;
$color-info-active-dark: #1554ad;
$color-info-text-hover-dark: #3c89e8;
$color-info-text-dark: #1668dc;
$color-info-text-active-dark: #1554ad;
$color-link-hover-dark: #15417e;
$color-link-active-dark: #1554ad;
$color-bg-mask-dark: rgba(0, 0, 0, 0.45);
$color-white-dark: #fff;
$color-fill-content-dark: rgba(255, 255, 255, 0.12);
$color-fill-content-hover-dark: rgba(255, 255, 255, 0.18);
$color-fill-alter-dark: rgba(255, 255, 255, 0.04);
$color-bg-container-disabled-dark: rgba(255, 255, 255, 0.08);
$color-border-bg-dark: #333b40;
$color-split-dark: rgba(39, 15, 0, 0.25);
$color-text-placeholder-dark: rgba(255, 255, 255, 0.25);
$color-text-disabled-dark: rgba(255, 255, 255, 0.25);
$color-text-heading-dark: rgba(255, 255, 255, 0.85);
$color-text-label-dark: rgba(255, 255, 255, 0.65);
$color-text-description-dark: rgba(255, 255, 255, 0.45);
$color-text-light-solid-dark: #fff;
$color-highlight-dark: #dc4446;
$color-bg-text-hover-dark: rgba(255, 255, 255, 0.12);
$color-bg-text-active-dark: rgba(255, 255, 255, 0.18);
$color-icon-dark: rgba(255, 255, 255, 0.45);
$color-icon-hover-dark: rgba(255, 255, 255, 0.85);
$color-error-outline-dark: rgba(40, 0, 1, 0.63);
$color-warning-outline-dark: rgba(40, 23, 0, 0.73);
$box-shadow-dark: 0 6px 16px 0 rgba(0, 0, 0, 0.08),
  0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
$box-shadow-secondary-dark: 0 6px 16px 0 rgba(0, 0, 0, 0.08),
  0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
$box-shadow-tertiary-dark: 0 1px 2px 0 rgba(0, 0, 0, 0.03),
  0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
$box-shadow-popover-arrow-dark: 2px 2px 5px rgba(0, 0, 0, 0.05);
$box-shadow-card-dark: 0 1px 2px -2px rgba(0, 0, 0, 0.16),
  0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);
$box-shadow-drawer-right-dark: -6px 0 16px 0 rgba(0, 0, 0, 0.08),
  -3px 0 6px -4px rgba(0, 0, 0, 0.12), -9px 0 28px 8px rgba(0, 0, 0, 0.05);
$box-shadow-drawer-left-dark: 6px 0 16px 0 rgba(0, 0, 0, 0.08),
  3px 0 6px -4px rgba(0, 0, 0, 0.12), 9px 0 28px 8px rgba(0, 0, 0, 0.05);
$box-shadow-drawer-up-dark: 0 6px 16px 0 rgba(0, 0, 0, 0.08),
  0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
$box-shadow-drawer-down-dark: 0 -6px 16px 0 rgba(0, 0, 0, 0.08),
  0 -3px 6px -4px rgba(0, 0, 0, 0.12), 0 -9px 28px 8px rgba(0, 0, 0, 0.05);
$box-shadow-tabs-overflow-left-dark: inset 10px 0 8px -8px rgba(0, 0, 0, 0.08);
$box-shadow-tabs-overflow-right-dark: inset -10px 0 8px -8px rgba(0, 0, 0, 0.08);
$box-shadow-tabs-overflow-top-dark: inset 0 10px 8px -8px rgba(0, 0, 0, 0.08);
$box-shadow-tabs-overflow-bottom-dark: inset 0 -10px 8px -8px
  rgba(0, 0, 0, 0.08);
